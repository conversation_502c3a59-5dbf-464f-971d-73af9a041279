using Godot;

public partial class BuildMenu : Canvas<PERSON>ayer, IMenu
{
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;
	private Button _anvilBuildButton;
	private Button _bridgeBuildButton;
	private Button _campfireBuildButton;
	private Button _furnace1BuildButton;
	private Button _furnace2BuildButton;
	private Button _furnace3BuildButton;
	private Button _furnace4BuildButton;
	private Button _grindstoneBuildButton;
	private Button _workbenchBuildButton;
	private Button _seedMakerBuildButton;
	private Button _cheesePressButton;
	private Button _kegButton;
	private Button _mayoMakerButton;
	private Button _smokerButton;
	private Button _jamMakerButton;
	private Button _oilMakerButton;
	// Task 50 - Windmill button
	private Button _windmillButton;
	// Task 53 - Palisade button
	private Button _palisadeButton;
	// Task 54 - TeslaCoil button
	private Button _teslaCoilButton;
	private Sprite2D _anvilBuildButtonSprite;
	private Sprite2D _bridgeBuildButtonSprite;
	private Sprite2D _campfireBuildButtonSprite;
	private Sprite2D _furnace1BuildButtonSprite;
	private Sprite2D _furnace2BuildButtonSprite;
	private Sprite2D _furnace3BuildButtonSprite;
	private Sprite2D _furnace4BuildButtonSprite;
	private Sprite2D _grindstoneBuildButtonSprite;
	private Sprite2D _workbenchBuildButtonSprite;
	private Sprite2D _seedMakerBuildButtonSprite;
	private Sprite2D _cheesePressButtonSprite;
	private Sprite2D _kegButtonSprite;
	private Sprite2D _mayoMakerButtonSprite;
	private Sprite2D _smokerButtonSprite;
	private Sprite2D _jamMakerButtonSprite;
	private Sprite2D _oilMakerButtonSprite;
	// Task 50 - Windmill button sprite
	private Sprite2D _windmillButtonSprite;
	// Task 53 - Palisade button sprite
	private Sprite2D _palisadeButtonSprite;
	// Task 54 - TeslaCoil button sprite
	private Sprite2D _teslaCoilButtonSprite;

	// Anvil building requirements
	public static readonly int ANVIL_WOOD_REQUIRED = 5;
	public static readonly int ANVIL_STONE_REQUIRED = 5;

	// Bridge building requirements
	public static readonly int BRIDGE_PLANK_REQUIRED = 5;

	// Campfire building requirements
	public static readonly int CAMPFIRE_WOOD_REQUIRED = 5;
	public static readonly int CAMPFIRE_STONE_REQUIRED = 2;

	// Furnace1 building requirements
	public static readonly int FURNACE1_STONEBRICK_REQUIRED = 20;
	public static readonly int FURNACE1_STONE2BRICK_REQUIRED = 20;

	// Furnace2 building requirements
	public static readonly int FURNACE2_IRONBAR_REQUIRED = 20;
	public static readonly int FURNACE2_COPPERBAR_REQUIRED = 20;
	public static readonly int FURNACE2_GOLDORE_REQUIRED = 20;

	// Furnace3 building requirements
	public static readonly int FURNACE3_GOLDBAR_REQUIRED = 20;
	public static readonly int FURNACE3_INDIGOSIUMBAR_REQUIRED = 20;
	public static readonly int FURNACE3_MITHRILORE_REQUIRED = 20;

	// Furnace4 building requirements
	public static readonly int FURNACE4_MITHRILBAR_REQUIRED = 20;
	public static readonly int FURNACE4_ERITHRYDIUMBAR_REQUIRED = 20;
	public static readonly int FURNACE4_ADAMANTITEORE_REQUIRED = 20;

	// Grindstone building requirements
	public static readonly int GRINDSTONE_WOODENBEAM_REQUIRED = 20;
	public static readonly int GRINDSTONE_STONEBRICK_REQUIRED = 10;
	public static readonly int GRINDSTONE_STONE2BRICK_REQUIRED = 10;

	// Workbench building requirements
	public static readonly int WORKBENCH_PLANK_REQUIRED = 20;
	public static readonly int WORKBENCH_STONEBRICK_REQUIRED = 10;

	// SeedMaker building requirements
	public static readonly int SEEDMAKER_STONEBRICK_REQUIRED = 10;
	public static readonly int SEEDMAKER_PLANK_REQUIRED = 15;

	// CheesePress building requirements
	public static readonly int CHEESE_PRESS_PLANK_REQUIRED = 15;
	public static readonly int CHEESE_PRESS_IRON_BAR_REQUIRED = 10;

	// Keg building requirements
	public static readonly int KEG_PLANK_REQUIRED = 20;
	public static readonly int KEG_IRON_BAR_REQUIRED = 8;

	// MayoMaker building requirements
	public static readonly int MAYO_MAKER_PLANK_REQUIRED = 12;
	public static readonly int MAYO_MAKER_IRON_BAR_REQUIRED = 6;

	// Smoker building requirements
	public static readonly int SMOKER_PLANK_REQUIRED = 18;
	public static readonly int SMOKER_IRON_BAR_REQUIRED = 7;

	// JamMaker building requirements
	public static readonly int JAM_MAKER_PLANK_REQUIRED = 14;
	public static readonly int JAM_MAKER_IRON_BAR_REQUIRED = 9;

	// OilMaker building requirements
	public static readonly int OIL_MAKER_PLANK_REQUIRED = 16;
	public static readonly int OIL_MAKER_IRON_BAR_REQUIRED = 8;

	// Task 50 - Windmill building requirements
	public static readonly int WINDMILL_PLANK_REQUIRED = 25;
	public static readonly int WINDMILL_IRONBAR_REQUIRED = 12;

	// Task 53 - Palisade building requirements
	public static readonly int PALISADE_WOODENBEAM_REQUIRED = 5;

	// Task 54 - TeslaCoil building requirements
	public static readonly int TESLACOIL_IRONBAR_REQUIRED = 10;
	public static readonly int TESLACOIL_PLANK_REQUIRED = 5;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		if (_animationPlayer == null)
		{
			GD.PrintErr("BuildMenu: AnimationPlayer node not found!");
			return;
		}

		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		if (_closeButton == null)
		{
			GD.PrintErr("BuildMenu: CloseButton node not found!");
			return;
		}

		_anvilBuildButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListAnvil/Button");
		if (_anvilBuildButton == null)
		{
			GD.PrintErr("BuildMenu: Anvil build button not found!");
			return;
		}

		_bridgeBuildButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListBridge/Button");
		if (_bridgeBuildButton == null)
		{
			GD.PrintErr("BuildMenu: Bridge build button not found!");
			return;
		}

		_campfireBuildButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCampfire/Button");
		if (_campfireBuildButton == null)
		{
			GD.PrintErr("BuildMenu: Campfire build button not found!");
			return;
		}

		_anvilBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListAnvil/BuildButton");
		if (_anvilBuildButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Anvil BuildButton sprite not found!");
			return;
		}

		_bridgeBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListBridge/BuildButton");
		if (_bridgeBuildButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Bridge BuildButton sprite not found!");
			return;
		}

		_campfireBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCampfire/BuildButton");
		if (_campfireBuildButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Campfire BuildButton sprite not found!");
			return;
		}

		_furnace1BuildButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace1/Button");
		if (_furnace1BuildButton == null)
		{
			GD.PrintErr("BuildMenu: Furnace1 build button not found!");
			return;
		}

		_furnace1BuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace1/BuildButton");
		if (_furnace1BuildButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Furnace1 BuildButton sprite not found!");
			return;
		}

		_furnace2BuildButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace2/Button");
		if (_furnace2BuildButton == null)
		{
			GD.PrintErr("BuildMenu: Furnace2 build button not found!");
			return;
		}

		_furnace2BuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace2/BuildButton");
		if (_furnace2BuildButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Furnace2 BuildButton sprite not found!");
			return;
		}

		_furnace3BuildButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace3/Button");
		if (_furnace3BuildButton == null)
		{
			GD.PrintErr("BuildMenu: Furnace3 build button not found!");
			return;
		}

		_furnace3BuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace3/BuildButton");
		if (_furnace3BuildButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Furnace3 BuildButton sprite not found!");
			return;
		}

		_furnace4BuildButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace4/Button");
		if (_furnace4BuildButton == null)
		{
			GD.PrintErr("BuildMenu: Furnace4 build button not found!");
			return;
		}

		_furnace4BuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListFurnace4/BuildButton");
		if (_furnace4BuildButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Furnace4 BuildButton sprite not found!");
			return;
		}

		_grindstoneBuildButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListGrindstone/Button");
		if (_grindstoneBuildButton == null)
		{
			GD.PrintErr("BuildMenu: Grindstone build button not found!");
			return;
		}

		_grindstoneBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListGrindstone/BuildButton");
		if (_grindstoneBuildButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Grindstone BuildButton sprite not found!");
			return;
		}

		_workbenchBuildButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListWorkbench/Button");
		if (_workbenchBuildButton == null)
		{
			GD.PrintErr("BuildMenu: Workbench build button not found!");
			return;
		}

		_workbenchBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListWorkbench/BuildButton");
		if (_workbenchBuildButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Workbench BuildButton sprite not found!");
			return;
		}

		_seedMakerBuildButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker/Button");
		if (_seedMakerBuildButton == null)
		{
			GD.PrintErr("BuildMenu: SeedMaker build button not found!");
			return;
		}

		_seedMakerBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListSeedMaker/BuildButton");
		if (_seedMakerBuildButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: SeedMaker BuildButton sprite not found!");
			return;
		}

		// Task 49 - New production buildings
		_cheesePressButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCheesePress/Button");
		if (_cheesePressButton == null)
		{
			GD.PrintErr("BuildMenu: CheesePress build button not found!");
			return;
		}

		_cheesePressButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCheesePress/BuildButton");
		if (_cheesePressButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: CheesePress BuildButton sprite not found!");
			return;
		}

		_kegButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListKeg/Button");
		if (_kegButton == null)
		{
			GD.PrintErr("BuildMenu: Keg build button not found!");
			return;
		}

		_kegButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListKeg/BuildButton");
		if (_kegButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Keg BuildButton sprite not found!");
			return;
		}

		_mayoMakerButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListMayoMaker/Button");
		if (_mayoMakerButton == null)
		{
			GD.PrintErr("BuildMenu: MayoMaker build button not found!");
			return;
		}

		_mayoMakerButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListMayoMaker/BuildButton");
		if (_mayoMakerButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: MayoMaker BuildButton sprite not found!");
			return;
		}

		_smokerButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListSmoker/Button");
		if (_smokerButton == null)
		{
			GD.PrintErr("BuildMenu: Smoker build button not found!");
			return;
		}

		_smokerButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListSmoker/BuildButton");
		if (_smokerButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Smoker BuildButton sprite not found!");
			return;
		}

		_jamMakerButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListJamMaker/Button");
		if (_jamMakerButton == null)
		{
			GD.PrintErr("BuildMenu: JamMaker build button not found!");
			return;
		}

		_jamMakerButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListJamMaker/BuildButton");
		if (_jamMakerButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: JamMaker BuildButton sprite not found!");
			return;
		}

		_oilMakerButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListOilMaker/Button");
		if (_oilMakerButton == null)
		{
			GD.PrintErr("BuildMenu: OilMaker build button not found!");
			return;
		}

		_oilMakerButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListOilMaker/BuildButton");
		if (_oilMakerButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: OilMaker BuildButton sprite not found!");
			return;
		}

		// Task 50 - Windmill button initialization
		_windmillButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListWindmill/Button");
		if (_windmillButton == null)
		{
			GD.PrintErr("BuildMenu: Windmill build button not found!");
			return;
		}

		_windmillButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListWindmill/BuildButton");
		if (_windmillButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Windmill BuildButton sprite not found!");
			return;
		}

		// Task 53 - Palisade button initialization
		_palisadeButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListPalisade/Button");
		if (_palisadeButton == null)
		{
			GD.PrintErr("BuildMenu: Palisade build button not found!");
			return;
		}

		_palisadeButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListPalisade/BuildButton");
		if (_palisadeButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: Palisade BuildButton sprite not found!");
			return;
		}

		// Task 54 - TeslaCoil button initialization
		_teslaCoilButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListTeslaCoil/Button");
		if (_teslaCoilButton == null)
		{
			GD.PrintErr("BuildMenu: TeslaCoil build button not found!");
			return;
		}

		_teslaCoilButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListTeslaCoil/BuildButton");
		if (_teslaCoilButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: TeslaCoil BuildButton sprite not found!");
			return;
		}

		_closeButton.Pressed += OnCloseButtonPressed;
		_anvilBuildButton.Pressed += OnAnvilBuildButtonPressed;
		_bridgeBuildButton.Pressed += OnBridgeBuildButtonPressed;
		_campfireBuildButton.Pressed += OnCampfireBuildButtonPressed;
		_furnace1BuildButton.Pressed += OnFurnace1BuildButtonPressed;
		_furnace2BuildButton.Pressed += OnFurnace2BuildButtonPressed;
		_furnace3BuildButton.Pressed += OnFurnace3BuildButtonPressed;
		_furnace4BuildButton.Pressed += OnFurnace4BuildButtonPressed;
		_grindstoneBuildButton.Pressed += OnGrindstoneBuildButtonPressed;
		_workbenchBuildButton.Pressed += OnWorkbenchBuildButtonPressed;
		_seedMakerBuildButton.Pressed += OnSeedMakerBuildButtonPressed;
		// Task 49 - New production building event connections
		_cheesePressButton.Pressed += OnCheesePressButtonPressed;
		_kegButton.Pressed += OnKegButtonPressed;
		_mayoMakerButton.Pressed += OnMayoMakerButtonPressed;
		_smokerButton.Pressed += OnSmokerButtonPressed;
		_jamMakerButton.Pressed += OnJamMakerButtonPressed;
		_oilMakerButton.Pressed += OnOilMakerButtonPressed;
		// Task 50 - Windmill event connection
		_windmillButton.Pressed += OnWindmillButtonPressed;
		// Task 53 - Palisade event connection
		_palisadeButton.Pressed += OnPalisadeButtonPressed;
		// Task 54 - TeslaCoil event connection
		_teslaCoilButton.Pressed += OnTeslaCoilButtonPressed;

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("RESET");
		}

		// Register with MenuManager (deferred to ensure MenuManager is ready)
		CallDeferred(nameof(RegisterWithMenuManager));
	}

	private void RegisterWithMenuManager()
	{
		if (MenuManager.Instance != null)
		{
			// Always re-register to ensure we're in the registry
			MenuManager.Instance.UnregisterMenu("BuildMenu");
			MenuManager.Instance.RegisterMenu("BuildMenu", this);
			GD.Print("BuildMenu: Successfully registered with MenuManager");
		}
		else
		{
			GD.PrintErr("BuildMenu: MenuManager.Instance is null, retrying in 0.1 seconds");
			GetTree().CreateTimer(0.1f).Timeout += RegisterWithMenuManager;
		}
	}

	public void OpenMenu()
	{
		// Update button states based on available resources
		UpdateBuildButtonStates();

		// Disable player movement when build menu opens
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		if (_animationPlayer != null && IsInstanceValid(_animationPlayer))
		{
			_animationPlayer.Play("Open");
		}
	}

	public void CloseMenu()
	{
		// Re-enable player movement when build menu closes
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);

		if (_animationPlayer != null && IsInstanceValid(_animationPlayer))
		{
			_animationPlayer.Play("Close");
		}
	}

	public bool IsMenuOpen()
	{
		return GetNode<Sprite2D>("Control/Panel").Visible;
	}

	private void OnCloseButtonPressed()
	{
		// Use MenuManager to close the menu properly
		MenuManager.Instance?.CloseMenu("BuildMenu");
	}

	private void OnAnvilBuildButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordAnvil())
		{
			GD.Print("BuildMenu: Not enough resources to build anvil!");
			return;
		}

		// Use MenuManager to close the menu properly
		MenuManager.Instance?.CloseMenu("BuildMenu");

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingAnvil();
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnBridgeBuildButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordBridge())
		{
			GD.Print("BuildMenu: Not enough resources to build bridge!");
			return;
		}

		// Use MenuManager to close the menu properly
		MenuManager.Instance?.CloseMenu("BuildMenu");

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingBridge();
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnCampfireBuildButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordCampfire())
		{
			GD.Print("BuildMenu: Not enough resources to build campfire!");
			return;
		}

		// Use MenuManager to close the menu properly
		MenuManager.Instance?.CloseMenu("BuildMenu");

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingCampfire();
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnFurnace1BuildButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordFurnace1())
		{
			GD.Print("BuildMenu: Not enough resources to build furnace1!");
			return;
		}

		// Use MenuManager to close the menu properly
		MenuManager.Instance?.CloseMenu("BuildMenu");

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingFurnace1();
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnFurnace2BuildButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordFurnace2())
		{
			GD.Print("BuildMenu: Not enough resources to build furnace2!");
			return;
		}

		// Use MenuManager to close the menu properly
		MenuManager.Instance?.CloseMenu("BuildMenu");

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingFurnace2();
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnFurnace3BuildButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordFurnace3())
		{
			GD.Print("BuildMenu: Not enough resources to build furnace3!");
			return;
		}

		// Use MenuManager to close the menu properly
		MenuManager.Instance?.CloseMenu("BuildMenu");

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingFurnace3();
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnFurnace4BuildButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordFurnace4())
		{
			GD.Print("BuildMenu: Not enough resources to build furnace4!");
			return;
		}

		// Use MenuManager to close the menu properly
		MenuManager.Instance?.CloseMenu("BuildMenu");

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingFurnace4();
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnGrindstoneBuildButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordGrindstone())
		{
			GD.Print("BuildMenu: Not enough resources to build grindstone!");
			return;
		}

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingGrindstone();
			// Use MenuManager to close the menu properly
			MenuManager.Instance?.CloseMenu("BuildMenu");
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnWorkbenchBuildButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordWorkbench())
		{
			GD.Print("BuildMenu: Not enough resources to build workbench!");
			return;
		}

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingWorkbench();
			// Use MenuManager to close the menu properly
			MenuManager.Instance?.CloseMenu("BuildMenu");
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnSeedMakerBuildButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordSeedMaker())
		{
			GD.Print("BuildMenu: Not enough resources to build seed maker!");
			return;
		}

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingSeedMaker();
			// Use MenuManager to close the menu properly
			MenuManager.Instance?.CloseMenu("BuildMenu");
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	// Task 49 - New production building event handlers
	private void OnCheesePressButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordCheesePress())
		{
			GD.Print("BuildMenu: Not enough resources to build cheese press!");
			return;
		}

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingCheesePress();
			// Use MenuManager to close the menu properly
			MenuManager.Instance?.CloseMenu("BuildMenu");
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnKegButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordKeg())
		{
			GD.Print("BuildMenu: Not enough resources to build keg!");
			return;
		}

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingKeg();
			// Use MenuManager to close the menu properly
			MenuManager.Instance?.CloseMenu("BuildMenu");
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnMayoMakerButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordMayoMaker())
		{
			GD.Print("BuildMenu: Not enough resources to build mayo maker!");
			return;
		}

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingMayoMaker();
			// Use MenuManager to close the menu properly
			MenuManager.Instance?.CloseMenu("BuildMenu");
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnSmokerButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordSmoker())
		{
			GD.Print("BuildMenu: Not enough resources to build smoker!");
			return;
		}

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingSmoker();
			// Use MenuManager to close the menu properly
			MenuManager.Instance?.CloseMenu("BuildMenu");
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnJamMakerButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordJamMaker())
		{
			GD.Print("BuildMenu: Not enough resources to build jam maker!");
			return;
		}

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingJamMaker();
			// Use MenuManager to close the menu properly
			MenuManager.Instance?.CloseMenu("BuildMenu");
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	private void OnOilMakerButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordOilMaker())
		{
			GD.Print("BuildMenu: Not enough resources to build oil maker!");
			return;
		}

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingOilMaker();
			// Use MenuManager to close the menu properly
			MenuManager.Instance?.CloseMenu("BuildMenu");
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	// Task 50 - Windmill event handler
	private void OnWindmillButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordWindmill())
		{
			GD.Print("BuildMenu: Not enough resources to build windmill!");
			return;
		}

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingWindmill();
			// Use MenuManager to close the menu properly
			MenuManager.Instance?.CloseMenu("BuildMenu");
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	// Task 53 - Palisade event handler
	private void OnPalisadeButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordPalisade())
		{
			GD.Print("BuildMenu: Not enough resources to build palisade!");
			return;
		}

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingPalisade();
			// Use MenuManager to close the menu properly
			MenuManager.Instance?.CloseMenu("BuildMenu");
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	// Task 54 - TeslaCoil event handler
	private void OnTeslaCoilButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordTeslaCoil())
		{
			GD.Print("BuildMenu: Not enough resources to build tesla coil!");
			return;
		}

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingTeslaCoil();
			// Use MenuManager to close the menu properly
			MenuManager.Instance?.CloseMenu("BuildMenu");
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	/// <summary>
	/// Check if player has enough resources to build an anvil
	/// </summary>
	private bool CanAffordAnvil()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		bool hasWood = resourcesManager.HasResource(ResourceType.Wood, ANVIL_WOOD_REQUIRED);
		bool hasStone = resourcesManager.HasResource(ResourceType.Stone, ANVIL_STONE_REQUIRED);

		return hasWood && hasStone;
	}

	/// <summary>
	/// Check if player has enough resources to build a bridge
	/// </summary>
	private bool CanAffordBridge()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		bool hasPlank = resourcesManager.HasResource(ResourceType.Plank, BRIDGE_PLANK_REQUIRED);

		return hasPlank;
	}

	/// <summary>
	/// Update the build button visual states based on available resources
	/// </summary>
	private void UpdateBuildButtonStates()
	{
		// Update anvil button
		bool canAffordAnvil = CanAffordAnvil();
		if (_anvilBuildButtonSprite != null && IsInstanceValid(_anvilBuildButtonSprite))
		{
			if (canAffordAnvil)
			{
				_anvilBuildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_anvilBuildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_anvilBuildButton != null && IsInstanceValid(_anvilBuildButton))
		{
			_anvilBuildButton.Disabled = !canAffordAnvil;
		}

		// Update bridge button
		bool canAffordBridge = CanAffordBridge();
		if (_bridgeBuildButtonSprite != null && IsInstanceValid(_bridgeBuildButtonSprite))
		{
			if (canAffordBridge)
			{
				_bridgeBuildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_bridgeBuildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_bridgeBuildButton != null && IsInstanceValid(_bridgeBuildButton))
		{
			_bridgeBuildButton.Disabled = !canAffordBridge;
		}

		// Update campfire button
		bool canAffordCampfire = CanAffordCampfire();
		if (_campfireBuildButtonSprite != null && IsInstanceValid(_campfireBuildButtonSprite))
		{
			if (canAffordCampfire)
			{
				_campfireBuildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_campfireBuildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_campfireBuildButton != null && IsInstanceValid(_campfireBuildButton))
		{
			_campfireBuildButton.Disabled = !canAffordCampfire;
		}

		// Update furnace1 button
		bool canAffordFurnace1 = CanAffordFurnace1();
		if (_furnace1BuildButtonSprite != null && IsInstanceValid(_furnace1BuildButtonSprite))
		{
			if (canAffordFurnace1)
			{
				_furnace1BuildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_furnace1BuildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_furnace1BuildButton != null && IsInstanceValid(_furnace1BuildButton))
		{
			_furnace1BuildButton.Disabled = !canAffordFurnace1;
		}

		// Update furnace2 button
		bool canAffordFurnace2 = CanAffordFurnace2();
		if (_furnace2BuildButtonSprite != null && IsInstanceValid(_furnace2BuildButtonSprite))
		{
			if (canAffordFurnace2)
			{
				_furnace2BuildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_furnace2BuildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_furnace2BuildButton != null && IsInstanceValid(_furnace2BuildButton))
		{
			_furnace2BuildButton.Disabled = !canAffordFurnace2;
		}

		// Update furnace3 button
		bool canAffordFurnace3 = CanAffordFurnace3();
		if (_furnace3BuildButtonSprite != null && IsInstanceValid(_furnace3BuildButtonSprite))
		{
			if (canAffordFurnace3)
			{
				_furnace3BuildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_furnace3BuildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_furnace3BuildButton != null && IsInstanceValid(_furnace3BuildButton))
		{
			_furnace3BuildButton.Disabled = !canAffordFurnace3;
		}

		// Update furnace4 button
		bool canAffordFurnace4 = CanAffordFurnace4();
		if (_furnace4BuildButtonSprite != null && IsInstanceValid(_furnace4BuildButtonSprite))
		{
			if (canAffordFurnace4)
			{
				_furnace4BuildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_furnace4BuildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_furnace4BuildButton != null && IsInstanceValid(_furnace4BuildButton))
		{
			_furnace4BuildButton.Disabled = !canAffordFurnace4;
		}

		// Update grindstone button
		bool canAffordGrindstone = CanAffordGrindstone();
		if (_grindstoneBuildButtonSprite != null && IsInstanceValid(_grindstoneBuildButtonSprite))
		{
			if (canAffordGrindstone)
			{
				_grindstoneBuildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_grindstoneBuildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_grindstoneBuildButton != null && IsInstanceValid(_grindstoneBuildButton))
		{
			_grindstoneBuildButton.Disabled = !canAffordGrindstone;
		}

		// Update workbench button
		bool canAffordWorkbench = CanAffordWorkbench();
		if (_workbenchBuildButtonSprite != null && IsInstanceValid(_workbenchBuildButtonSprite))
		{
			if (canAffordWorkbench)
			{
				_workbenchBuildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_workbenchBuildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_workbenchBuildButton != null && IsInstanceValid(_workbenchBuildButton))
		{
			_workbenchBuildButton.Disabled = !canAffordWorkbench;
		}

		// Update seed maker button
		bool canAffordSeedMaker = CanAffordSeedMaker();
		if (_seedMakerBuildButtonSprite != null && IsInstanceValid(_seedMakerBuildButtonSprite))
		{
			if (canAffordSeedMaker)
			{
				_seedMakerBuildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_seedMakerBuildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_seedMakerBuildButton != null && IsInstanceValid(_seedMakerBuildButton))
		{
			_seedMakerBuildButton.Disabled = !canAffordSeedMaker;
		}

		// Task 49 - Update new production building buttons
		bool canAffordCheesePress = CanAffordCheesePress();
		if (_cheesePressButtonSprite != null && IsInstanceValid(_cheesePressButtonSprite))
		{
			if (canAffordCheesePress)
			{
				_cheesePressButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_cheesePressButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_cheesePressButton != null && IsInstanceValid(_cheesePressButton))
		{
			_cheesePressButton.Disabled = !canAffordCheesePress;
		}

		bool canAffordKeg = CanAffordKeg();
		if (_kegButtonSprite != null && IsInstanceValid(_kegButtonSprite))
		{
			if (canAffordKeg)
			{
				_kegButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_kegButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_kegButton != null && IsInstanceValid(_kegButton))
		{
			_kegButton.Disabled = !canAffordKeg;
		}

		bool canAffordMayoMaker = CanAffordMayoMaker();
		if (_mayoMakerButtonSprite != null && IsInstanceValid(_mayoMakerButtonSprite))
		{
			if (canAffordMayoMaker)
			{
				_mayoMakerButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_mayoMakerButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_mayoMakerButton != null && IsInstanceValid(_mayoMakerButton))
		{
			_mayoMakerButton.Disabled = !canAffordMayoMaker;
		}

		bool canAffordSmoker = CanAffordSmoker();
		if (_smokerButtonSprite != null && IsInstanceValid(_smokerButtonSprite))
		{
			if (canAffordSmoker)
			{
				_smokerButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_smokerButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_smokerButton != null && IsInstanceValid(_smokerButton))
		{
			_smokerButton.Disabled = !canAffordSmoker;
		}

		bool canAffordJamMaker = CanAffordJamMaker();
		if (_jamMakerButtonSprite != null && IsInstanceValid(_jamMakerButtonSprite))
		{
			if (canAffordJamMaker)
			{
				_jamMakerButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_jamMakerButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_jamMakerButton != null && IsInstanceValid(_jamMakerButton))
		{
			_jamMakerButton.Disabled = !canAffordJamMaker;
		}

		bool canAffordOilMaker = CanAffordOilMaker();
		if (_oilMakerButtonSprite != null && IsInstanceValid(_oilMakerButtonSprite))
		{
			if (canAffordOilMaker)
			{
				_oilMakerButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_oilMakerButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_oilMakerButton != null && IsInstanceValid(_oilMakerButton))
		{
			_oilMakerButton.Disabled = !canAffordOilMaker;
		}

		// Task 50 - Update Windmill button
		bool canAffordWindmill = CanAffordWindmill();
		if (_windmillButtonSprite != null && IsInstanceValid(_windmillButtonSprite))
		{
			if (canAffordWindmill)
			{
				_windmillButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_windmillButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_windmillButton != null && IsInstanceValid(_windmillButton))
		{
			_windmillButton.Disabled = !canAffordWindmill;
		}

		// Task 53 - Update Palisade button
		bool canAffordPalisade = CanAffordPalisade();
		if (_palisadeButtonSprite != null && IsInstanceValid(_palisadeButtonSprite))
		{
			if (canAffordPalisade)
			{
				_palisadeButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_palisadeButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_palisadeButton != null && IsInstanceValid(_palisadeButton))
		{
			_palisadeButton.Disabled = !canAffordPalisade;
		}

		// Task 54 - Update tesla coil button
		bool canAffordTeslaCoil = CanAffordTeslaCoil();
		if (_teslaCoilButtonSprite != null && IsInstanceValid(_teslaCoilButtonSprite))
		{
			if (canAffordTeslaCoil)
			{
				_teslaCoilButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_teslaCoilButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}
		if (_teslaCoilButton != null && IsInstanceValid(_teslaCoilButton))
		{
			_teslaCoilButton.Disabled = !canAffordTeslaCoil;
		}
	}

	public override void _ExitTree()
	{
		if (_closeButton != null)
		{
			_closeButton.Pressed -= OnCloseButtonPressed;
		}
		if (_anvilBuildButton != null)
		{
			_anvilBuildButton.Pressed -= OnAnvilBuildButtonPressed;
		}
		if (_bridgeBuildButton != null)
		{
			_bridgeBuildButton.Pressed -= OnBridgeBuildButtonPressed;
		}
		if (_campfireBuildButton != null)
		{
			_campfireBuildButton.Pressed -= OnCampfireBuildButtonPressed;
		}
		if (_furnace1BuildButton != null)
		{
			_furnace1BuildButton.Pressed -= OnFurnace1BuildButtonPressed;
		}
		if (_furnace2BuildButton != null)
		{
			_furnace2BuildButton.Pressed -= OnFurnace2BuildButtonPressed;
		}
		if (_furnace3BuildButton != null)
		{
			_furnace3BuildButton.Pressed -= OnFurnace3BuildButtonPressed;
		}
		if (_furnace4BuildButton != null)
		{
			_furnace4BuildButton.Pressed -= OnFurnace4BuildButtonPressed;
		}
		if (_palisadeButton != null)
		{
			_palisadeButton.Pressed -= OnPalisadeButtonPressed;
		}

		// Unregister from MenuManager
		if (MenuManager.Instance != null)
		{
			MenuManager.Instance.UnregisterMenu("BuildMenu");
			GD.Print("BuildMenu: Unregistered from MenuManager");
		}
	}

	private bool CanAffordCampfire()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.Wood, CAMPFIRE_WOOD_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.Stone, CAMPFIRE_STONE_REQUIRED);
	}

	private bool CanAffordFurnace1()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.StoneBrick, FURNACE1_STONEBRICK_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.Stone2Brick, FURNACE1_STONE2BRICK_REQUIRED);
	}

	private bool CanAffordFurnace2()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.IronBar, FURNACE2_IRONBAR_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.CopperBar, FURNACE2_COPPERBAR_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.GoldOre, FURNACE2_GOLDORE_REQUIRED);
	}

	private bool CanAffordFurnace3()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.GoldBar, FURNACE3_GOLDBAR_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.IndigosiumBar, FURNACE3_INDIGOSIUMBAR_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.MithrilOre, FURNACE3_MITHRILORE_REQUIRED);
	}

	private bool CanAffordFurnace4()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.MithrilBar, FURNACE4_MITHRILBAR_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.ErithrydiumBar, FURNACE4_ERITHRYDIUMBAR_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.AdamantiteOre, FURNACE4_ADAMANTITEORE_REQUIRED);
	}

	private bool CanAffordGrindstone()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.WoodenBeam, GRINDSTONE_WOODENBEAM_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.StoneBrick, GRINDSTONE_STONEBRICK_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.Stone2Brick, GRINDSTONE_STONE2BRICK_REQUIRED);
	}

	private bool CanAffordWorkbench()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.Plank, WORKBENCH_PLANK_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.StoneBrick, WORKBENCH_STONEBRICK_REQUIRED);
	}

	private bool CanAffordSeedMaker()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.StoneBrick, SEEDMAKER_STONEBRICK_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.Plank, SEEDMAKER_PLANK_REQUIRED);
	}

	// Task 49 - New production building CanAfford methods
	private bool CanAffordCheesePress()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.Plank, CHEESE_PRESS_PLANK_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.IronBar, CHEESE_PRESS_IRON_BAR_REQUIRED);
	}

	private bool CanAffordKeg()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.Plank, KEG_PLANK_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.IronBar, KEG_IRON_BAR_REQUIRED);
	}

	private bool CanAffordMayoMaker()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.Plank, MAYO_MAKER_PLANK_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.IronBar, MAYO_MAKER_IRON_BAR_REQUIRED);
	}

	private bool CanAffordSmoker()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.Plank, SMOKER_PLANK_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.IronBar, SMOKER_IRON_BAR_REQUIRED);
	}

	private bool CanAffordJamMaker()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.Plank, JAM_MAKER_PLANK_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.IronBar, JAM_MAKER_IRON_BAR_REQUIRED);
	}

	private bool CanAffordOilMaker()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.Plank, OIL_MAKER_PLANK_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.IronBar, OIL_MAKER_IRON_BAR_REQUIRED);
	}

	// Task 50 - Windmill CanAfford method
	private bool CanAffordWindmill()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.Plank, WINDMILL_PLANK_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.IronBar, WINDMILL_IRONBAR_REQUIRED);
	}

	// Task 53 - Palisade CanAfford method
	private bool CanAffordPalisade()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.WoodenBeam, PALISADE_WOODENBEAM_REQUIRED);
	}

	private bool CanAffordTeslaCoil()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.IronBar, TESLACOIL_IRONBAR_REQUIRED) &&
			   resourcesManager.HasResource(ResourceType.Plank, TESLACOIL_PLANK_REQUIRED);
	}
}

using Godot;
using System;

public partial class DungeonPlayerController : CharacterBody2D
{
	[Export]
	public float Speed = 100.0f;

	[Export]
	public int TileSize { get; set; } = 16;

	private AnimationPlayer _animationPlayer;
	private Sprite2D _sprite;
	private Sprite2D _toolSprite;
	private Sprite2D _directionIndicator;
	private string _currentAnimation = "idle_down";
	private ToolType _currentTool = ToolType.None;
	private bool _isUsingTool = false;
	private string _lastDirection = "down";
	private PackedScene _arrowScene;
	private float _baseSpeed;
	private float _currentSpeedModifier = 1.0f;
	private bool _isMoving = false;
	private Area2D _playerDetector;
	private bool _movementEnabled = true;
	private double _lastSeedPlantTime = 0.0;
	private const double SEED_PLANT_COOLDOWN = 1.2;
	private float _lastBowShotTime = 0.0f;
	private const float BOW_COOLDOWN = 1.0f;
	private bool _swordSignalEmitted = false;
	private bool _pickaxeSignalEmitted = false;
	private Node2D _keyE;
	private AnimationPlayer _keyAnimationPlayer;
	private AnimationPlayer ShakeCameraAnimationPlayer;
	private PlayerLight _playerLight;
	private PackedScene _placeholderScene;
	private TilePlaceholder _tilePlaceholder;

	// Tool cooldowns
	private float _lastPickaxeUseTime = 0.0f;
	private float _lastSwordUseTime = 0.0f;
	private float _lastBowUseTime = 0.0f;
	private float _lastHoeUseTime = 0.0f;
	private float _lastHammerUseTime = 0.0f;
	private float _lastWateringCanUseTime = 0.0f;

	private const float PICKAXE_COOLDOWN = 1.0f;
	private const float SWORD_COOLDOWN = 0.8f;
	private const float BOW_COOLDOWN_CONST = 1.0f;
	private const float HOE_COOLDOWN = 1.0f;
	private const float HAMMER_COOLDOWN = 1.0f;
	private const float WATERING_CAN_COOLDOWN = 1.0f;

	// Rabbit leg consumption
	private float _lastRabbitLegUseTime = 0.0f;
	private const float _rabbitLegCooldown = 2.0f;

	[Export]
	public float IndicatorDistance = 25.0f;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("PlayerAnimation");
		_sprite = GetNode<Sprite2D>("PlayerSprite");
		_toolSprite = GetNode<Sprite2D>("Tool");
		_directionIndicator = GetNode<Sprite2D>("DirectionIndicator");
		_playerDetector = GetNode<Area2D>("PlayerDetector");
		_keyE = GetNode<Node2D>("Keys/KeyE");
		_keyAnimationPlayer = _keyE.GetNode<AnimationPlayer>("AnimationPlayer");
		ShakeCameraAnimationPlayer = GetNode<AnimationPlayer>("CameraAnimationPlayer");

		_baseSpeed = Speed;

		_toolSprite.Visible = false;
		_directionIndicator.Visible = false;

		_arrowScene = GD.Load<PackedScene>("res://scenes/arrow.tscn");

		// Initialize TilePlaceholder
		_placeholderScene = GD.Load<PackedScene>("res://scenes/TilePlaceholder.tscn");
		if (_placeholderScene != null)
		{
			_tilePlaceholder = _placeholderScene.Instantiate<TilePlaceholder>();
			_tilePlaceholder.TileSize = TileSize;

			GetParent().CallDeferred("add_child", _tilePlaceholder);

			_tilePlaceholder.GlobalPosition = GlobalPosition + new Vector2(TileSize, 0);
			_tilePlaceholder.Visible = true;

			GD.Print("DungeonPlayer: TilePlaceholder created and will be added to scene");
		}
		else
		{
			GD.PrintErr("DungeonPlayer: Failed to load TilePlaceholder scene");
		}

		LoadPlayerState();

		// Connect to movement control signals
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PlayerMovementEnabled += OnPlayerMovementEnabled;
			CommonSignals.Instance.UseResourceRequested += OnUseResourceRequested;
			CommonSignals.Instance.ShowKeyEPrompt += OnShowKeyEPrompt;
			CommonSignals.Instance.HideKeyEPrompt += OnHideKeyEPrompt;
		}

		// PlayerLight is now part of the scene, get reference to it
		_playerLight = GetNode<PlayerLight>("PlayerLight");
		if (_playerLight != null)
		{
			GD.Print("DungeonPlayer: PlayerLight component found in scene");
		}
		else
		{
			GD.PrintErr("DungeonPlayer: PlayerLight component not found in scene!");
		}
	}

	public override void _Process(double delta)
	{
		HandleToolInput();
		UpdateDirectionIndicator();
		UpdateTilePlaceholder();

		if (_isUsingTool || !_movementEnabled)
			return;

		Vector2 velocity = Vector2.Zero;

		if (Input.IsActionPressed("ui_up") || Input.IsKeyPressed(Key.W))
			velocity.Y -= 1;
		if (Input.IsActionPressed("ui_down") || Input.IsKeyPressed(Key.S))
			velocity.Y += 1;
		if (Input.IsActionPressed("ui_left") || Input.IsKeyPressed(Key.A))
			velocity.X -= 1;
		if (Input.IsActionPressed("ui_right") || Input.IsKeyPressed(Key.D))
			velocity.X += 1;

		if (velocity.Length() > 0)
			velocity = velocity.Normalized();

		_isMoving = velocity.Length() > 0;

		float statsSpeedModifier = PlayerStatsManager.Instance?.GetSpeedModifier() ?? 1.0f;
		Velocity = velocity * Speed * statsSpeedModifier;

		UpdateAnimation(velocity);
		Vector2 oldPosition = GlobalPosition;
		MoveAndSlide();

		// Update GameData if position changed
		if (GlobalPosition != oldPosition)
		{
			UpdatePlayerPositionInGameData();
		}
	}

	public override void _Input(InputEvent @event)
	{
		if (@event.IsActionPressed("Interact"))
		{
			CommonSignals.Instance?.EmitEKeyPressed();
		}
	}

	private void HandleToolInput()
	{
		bool useToolPressed = Input.IsActionPressed("UseTool");

		if (useToolPressed)
		{
			HandleToolUse();
		}
	}

	private void HandleToolUse()
	{
		if (_isUsingTool)
			return;

		var rm = ResourcesManager.Instance;
		if (rm != null)
		{
			var currentItem = rm.GetQuickSelectItem(GetCurrentSelectedSlot());

			if (currentItem != null && !currentItem.IsEmpty)
			{
				if (currentItem.IsTool)
				{
					if (_currentTool != ToolType.None)
					{
						UseTool();
					}
				}
			}
		}
	}

	private int GetCurrentSelectedSlot()
	{
		// Try to find SelectedToolPanel in current scene first
		var selectedToolPanel = GetTree().GetFirstNodeInGroup("selected_tool_panel") as SelectedToolPanel;
		
		// Fallback to checking both world and dungeon paths
		if (selectedToolPanel == null)
		{
			selectedToolPanel = GetNodeOrNull<SelectedToolPanel>("/root/world/SelectedToolPanel");
		}
		if (selectedToolPanel == null)
		{
			selectedToolPanel = GetNodeOrNull<SelectedToolPanel>("/root/Dungeon/SelectedToolPanel");
		}
		
		if (selectedToolPanel != null)
		{
			return selectedToolPanel.GetCurrentSelectedSlot();
		}
		return 0;
	}

	private void UseTool()
	{
		float currentTime = Time.GetTicksMsec() / 1000.0f;
		bool canUse = false;

		// Check tool-specific cooldowns
		switch (_currentTool)
		{
			case ToolType.Pickaxe:
				canUse = currentTime - _lastPickaxeUseTime >= PICKAXE_COOLDOWN;
				if (canUse) _lastPickaxeUseTime = currentTime;
				break;
			case ToolType.Sword:
				canUse = currentTime - _lastSwordUseTime >= SWORD_COOLDOWN;
				if (canUse) _lastSwordUseTime = currentTime;
				break;
			case ToolType.Bow:
				canUse = currentTime - _lastBowUseTime >= BOW_COOLDOWN_CONST;
				if (canUse) _lastBowUseTime = currentTime;
				break;
			case ToolType.Hoe:
				canUse = currentTime - _lastHoeUseTime >= HOE_COOLDOWN;
				if (canUse) _lastHoeUseTime = currentTime;
				break;
			case ToolType.Hammer:
				canUse = currentTime - _lastHammerUseTime >= HAMMER_COOLDOWN;
				if (canUse) _lastHammerUseTime = currentTime;
				break;
			case ToolType.WateringCan:
				canUse = currentTime - _lastWateringCanUseTime >= WATERING_CAN_COOLDOWN;
				if (canUse) _lastWateringCanUseTime = currentTime;
				break;
			default:
				canUse = true;
				break;
		}

		if (!canUse)
		{
			return;
		}

		_isUsingTool = true;
		_toolSprite.Visible = true;
		_swordSignalEmitted = false;
		_pickaxeSignalEmitted = false;

		UpdateDirectionIndicatorVisibility();

		// Update player direction based on mouse position for directional tools
		if (_currentTool == ToolType.Sword || _currentTool == ToolType.Bow)
		{
			Vector2 mousePos = GetGlobalMousePosition();
			Vector2 mouseDirection = (mousePos - GlobalPosition).Normalized();
			string newDirection = GetClosestCardinalDirection(mouseDirection);
			if (newDirection != _lastDirection)
			{
				_lastDirection = newDirection;
				UpdatePlayerDirectionInGameData();
			}
		}

		string toolAnimation = GetToolAnimation(_currentTool, _lastDirection);
		_currentAnimation = toolAnimation;

		if (_animationPlayer.HasAnimation(toolAnimation))
		{
			float animSpeedModifier = PlayerStatsManager.Instance?.GetAnimationSpeedModifier() ?? 1.0f;
			_animationPlayer.SpeedScale = animSpeedModifier;
			_animationPlayer.Play(toolAnimation);
			_animationPlayer.AnimationFinished += OnToolAnimationFinished;

			// Set up signal emission timing for specific tools
			if (_currentTool == ToolType.Sword)
			{
				float animationLength = (float)_animationPlayer.CurrentAnimationLength / animSpeedModifier;
				GetTree().CreateTimer(animationLength * 0.5f).Timeout += OnSwordMidAnimation;
			}
			else if (_currentTool == ToolType.Pickaxe)
			{
				// Pickaxe signal should be emitted after 0.65 seconds (adjusted for animation speed)
				float delayTime = 0.65f / animSpeedModifier;
				GetTree().CreateTimer(delayTime).Timeout += OnPickaxeDelayedAction;
			}
		}
		else
		{
			GD.PrintErr($"DungeonPlayer: Animation '{toolAnimation}' not found!");
			ExecuteToolAction();
			_isUsingTool = false;
			_toolSprite.Visible = false;
		}
	}

	private string GetToolAnimation(ToolType tool, string direction)
	{
		return tool switch
		{
			ToolType.Pickaxe => $"pickaxe_{direction}",
			ToolType.Sword => $"sword_{direction}",
			ToolType.Bow => $"bow_{direction}",
			ToolType.Hoe => $"hoe_{direction}",
			ToolType.Hammer => $"hammer_{direction}",
			ToolType.WateringCan => $"wateringcan_{direction}",
			_ => $"idle_{direction}"
		};
	}

	private string GetClosestCardinalDirection(Vector2 direction)
	{
		float absX = Math.Abs(direction.X);
		float absY = Math.Abs(direction.Y);

		if (absX > absY)
		{
			return direction.X > 0 ? "right" : "left";
		}
		else
		{
			return direction.Y > 0 ? "down" : "up";
		}
	}

	private void OnToolAnimationFinished(StringName animName)
	{
		_animationPlayer.AnimationFinished -= OnToolAnimationFinished;

		// Execute tool action for tools that don't have mid-animation timing
		if (_currentTool != ToolType.Sword && _currentTool != ToolType.Pickaxe)
		{
			ExecuteToolAction();
		}

		_isUsingTool = false;
		_toolSprite.Visible = false;

		UpdateDirectionIndicatorVisibility();

		string idleAnimation = $"idle_{_lastDirection}";
		_currentAnimation = idleAnimation;
		float animSpeedModifier = PlayerStatsManager.Instance?.GetAnimationSpeedModifier() ?? 1.0f;
		_animationPlayer.SpeedScale = animSpeedModifier;

		if (_animationPlayer.HasAnimation(idleAnimation))
		{
			_animationPlayer.Play(idleAnimation);
		}
	}

	private void OnSwordMidAnimation()
	{
		if (_currentTool == ToolType.Sword && !_swordSignalEmitted)
		{
			_swordSignalEmitted = true;
			ExecuteToolAction();
		}
	}

	private void OnPickaxeDelayedAction()
	{
		if (_currentTool == ToolType.Pickaxe && !_pickaxeSignalEmitted)
		{
			_pickaxeSignalEmitted = true;
			ExecuteToolAction();
		}
	}

	private void ExecuteToolAction()
	{
		Vector2I targetTile = GetLookingAtTile();

		CommonSignals.Instance?.EmitToolUsed();

		switch (_currentTool)
		{
			case ToolType.Bow:
				ShootArrow();
				_lastBowShotTime = Time.GetTicksMsec() / 1000.0f;
				break;
			case ToolType.Pickaxe:
				int pickaxeLevel = GameSaveData.Instance.PlayerStats.ToolLevels.TryGetValue(ToolType.Pickaxe, out int level) ? level : 1;
				int damage = pickaxeLevel + 2;
				GD.Print($"DungeonPlayer: Emitting pickaxe signal at tile {targetTile} with damage {damage}");
				CommonSignals.Instance?.EmitPickaxeUsed(targetTile, damage);
				break;
			case ToolType.Hammer:
				int hammerLevel = GameSaveData.Instance.PlayerStats.ToolLevels.TryGetValue(ToolType.Hammer, out int hammerLvl) ? hammerLvl : 1;
				int repairAmount = 2 + hammerLevel;
				CommonSignals.Instance?.EmitHammerUsed(targetTile, repairAmount);
				break;
			case ToolType.Hoe:
				CommonSignals.Instance?.EmitHoeUsed(targetTile);
				break;
			case ToolType.WateringCan:
				CommonSignals.Instance?.EmitWateringCanUsed(targetTile);
				break;
			case ToolType.Sword:
				Vector2 attackDirection = GetFacingDirectionVector();
				CommonSignals.Instance?.EmitSwordUsed(targetTile, GlobalPosition, attackDirection);
				break;
			default:
				GD.Print($"DungeonPlayer: {_currentTool} action - not implemented yet");
				break;
		}
	}

	public void SetCurrentTool(ToolType tool)
	{
		if (_currentTool != tool)
		{
			_currentTool = tool;
			UpdateSelectedToolInGameData();

			var textureManager = TextureManager.Instance;
			if (textureManager != null && _toolSprite != null)
			{
				var toolTexture = textureManager.GetToolTexture(tool);
				if (toolTexture != null)
				{
					_toolSprite.Texture = toolTexture;
				}
			}

			UpdateDirectionIndicatorVisibility();
			GD.Print($"DungeonPlayer: Tool changed to {tool}");
		}
	}

	public bool UseResource(ResourceType resourceType)
	{
		var rm = ResourcesManager.Instance;
		if (rm == null) return false;

		// Handle rabbit leg consumption with cooldown
		if (resourceType == ResourceType.RawRabbitLeg || resourceType == ResourceType.CookedRabbitLeg)
		{
			float currentTime = Time.GetTicksMsec() / 1000.0f;
			if (currentTime - _lastRabbitLegUseTime < _rabbitLegCooldown)
			{
				return false;
			}

			_lastRabbitLegUseTime = currentTime;

			if (resourceType == ResourceType.RawRabbitLeg)
			{
				PlayerStatsManager.Instance?.ConsumeRawRabbitLeg();
				if (rm.HasResource(ResourceType.RawRabbitLeg, 1))
				{
					rm.RemoveResource(ResourceType.RawRabbitLeg, 1);
					return true;
				}
				return false;
			}
			else if (resourceType == ResourceType.CookedRabbitLeg)
			{
				PlayerStatsManager.Instance?.ConsumeCookedRabbitLeg();
				if (rm.HasResource(ResourceType.CookedRabbitLeg, 1))
				{
					rm.RemoveResource(ResourceType.CookedRabbitLeg, 1);
					return true;
				}
				return false;
			}
		}

		return false;
	}

	private void UpdateDirectionIndicatorVisibility()
	{
		_directionIndicator.Visible = (_currentTool == ToolType.Bow || _currentTool == ToolType.Sword) && !_isUsingTool;
	}

	private void UpdateDirectionIndicator()
	{
		if (!_directionIndicator.Visible)
			return;

		Vector2 mousePos = GetGlobalMousePosition();
		Vector2 mouseDirection = (mousePos - GlobalPosition).Normalized();

		Vector2 validDirection;
		if (_currentTool == ToolType.Bow)
		{
			validDirection = mouseDirection;
		}
		else if (_currentTool == ToolType.Sword)
		{
			validDirection = mouseDirection;
		}
		else
		{
			validDirection = GetValidShootingDirection(mouseDirection);
		}

		Vector2 indicatorPos = GlobalPosition + validDirection * IndicatorDistance;
		_directionIndicator.GlobalPosition = indicatorPos;

		float angle = validDirection.Angle();
		_directionIndicator.Rotation = angle;
	}

	private Vector2 GetValidShootingDirection(Vector2 mouseDirection)
	{
		Vector2[] cardinalDirections = {
			Vector2.Up,
			Vector2.Down,
			Vector2.Left,
			Vector2.Right
		};

		Vector2 closestDirection = cardinalDirections[0];
		float closestDot = mouseDirection.Dot(closestDirection);

		foreach (Vector2 direction in cardinalDirections)
		{
			float dot = mouseDirection.Dot(direction);
			if (dot > closestDot)
			{
				closestDot = dot;
				closestDirection = direction;
			}
		}

		return closestDirection;
	}

	private void ShootArrow()
	{
		if (_arrowScene == null)
			return;

		Vector2 mousePos = GetGlobalMousePosition();
		Vector2 mouseDirection = (mousePos - GlobalPosition).Normalized();

		Arrow arrow = _arrowScene.Instantiate<Arrow>();

		arrow.GlobalPosition = GlobalPosition + mouseDirection * 10;

		arrow.Initialize(mouseDirection);

		GetTree().CurrentScene.AddChild(arrow);
	}

	private Vector2I GetLookingAtTile()
	{
		Vector2I playerTilePosition = new(
			Mathf.FloorToInt(GlobalPosition.X / TileSize),
			Mathf.FloorToInt(GlobalPosition.Y / TileSize)
		);

		Vector2I targetTile = playerTilePosition + GetFacingDirectionTileOffset();

		GD.Print($"DungeonPlayer: Player at {GlobalPosition} (tile {playerTilePosition}), facing {_lastDirection}, target tile {targetTile}");
		return targetTile;
	}

	private Vector2 GetFacingDirectionVector()
	{
		return _lastDirection switch
		{
			"up" => Vector2.Up,
			"down" => Vector2.Down,
			"left" => Vector2.Left,
			"right" => Vector2.Right,
			_ => Vector2.Down
		};
	}

	private void UpdateAnimation(Vector2 velocity)
	{
		if (_isUsingTool)
			return;

		string newAnimation = _currentAnimation;

		if (velocity.Length() == 0)
		{
			if (_currentAnimation.StartsWith("walk_"))
			{
				newAnimation = "idle_" + _currentAnimation[5..];
			}
		}
		else
		{
			string newDirection;
			if (Math.Abs(velocity.X) >= Math.Abs(velocity.Y))
			{
				newDirection = velocity.X > 0 ? "right" : "left";
				newAnimation = velocity.X > 0 ? "walk_right" : "walk_left";
			}
			else
			{
				newDirection = velocity.Y > 0 ? "down" : "up";
				newAnimation = velocity.Y > 0 ? "walk_down" : "walk_up";
			}

			if (newDirection != _lastDirection)
			{
				_lastDirection = newDirection;
				UpdatePlayerDirectionInGameData();
			}
		}

		if (newAnimation != _currentAnimation)
		{
			_currentAnimation = newAnimation;
			if (_animationPlayer.HasAnimation(newAnimation))
			{
				_animationPlayer.Play(newAnimation);
			}
		}
	}

	private void LoadPlayerState()
	{
		var gameData = GameSaveData.Instance;

		Vector2 savedPosition = gameData.PlayerStats.Position;
		if (savedPosition != Vector2.Zero)
		{
			GlobalPosition = savedPosition;
		}

		string savedDirection = gameData.PlayerStats.LastDirection;
		if (!string.IsNullOrEmpty(savedDirection))
		{
			_lastDirection = savedDirection;
		}

		ToolType savedTool = gameData.PlayerStats.SelectedTool;
		SetCurrentTool(savedTool);
	}

	private void UpdatePlayerPositionInGameData()
	{
		GameSaveData.Instance.PlayerStats.Position = GlobalPosition;
	}

	private void UpdatePlayerDirectionInGameData()
	{
		GameSaveData.Instance.PlayerStats.LastDirection = _lastDirection;
	}

	private void UpdateSelectedToolInGameData()
	{
		GameSaveData.Instance.PlayerStats.SelectedTool = _currentTool;
	}

	private void OnPlayerMovementEnabled(bool enabled)
	{
		_movementEnabled = enabled;
		GD.Print($"DungeonPlayer movement {(enabled ? "enabled" : "disabled")}");
	}

	private void OnUseResourceRequested(ResourceType resourceType)
	{
		bool success = UseResource(resourceType);
		CommonSignals.Instance?.EmitResourceUsed(resourceType, success);
	}

	private void OnShowKeyEPrompt()
	{
		if (_keyE != null && _keyAnimationPlayer != null)
		{
			_keyE.Visible = true;
			_keyAnimationPlayer.Play("PressKey");
		}
	}

	private void OnHideKeyEPrompt()
	{
		if (_keyE != null)
		{
			_keyE.Visible = false;
		}
	}

	public void TakeDamage(int damage)
	{
		var statsManager = PlayerStatsManager.Instance;
		if (statsManager == null) return;

		int currentHealth = statsManager.CurrentHealth;
		int newHealth = Math.Max(0, currentHealth - damage);
		int actualDamage = currentHealth - newHealth;

		statsManager.AddHealth(-actualDamage);

		GD.Print($"DungeonPlayer took {actualDamage} damage! Health: {newHealth}/{statsManager.MaxHealth}");

		if (newHealth <= 0)
		{
			GD.Print("DungeonPlayer health reached 0!");
		}
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PlayerMovementEnabled -= OnPlayerMovementEnabled;
			CommonSignals.Instance.UseResourceRequested -= OnUseResourceRequested;
			CommonSignals.Instance.ShowKeyEPrompt -= OnShowKeyEPrompt;
			CommonSignals.Instance.HideKeyEPrompt -= OnHideKeyEPrompt;
		}

		base._ExitTree();
	}

	private void UpdateTilePlaceholder()
	{
		if (_tilePlaceholder == null)
		{
			return;
		}

		if (!ShouldShowPlaceholder())
		{
			_tilePlaceholder.Hide();
			return;
		}

		Vector2I playerTilePosition = new(
			Mathf.FloorToInt(GlobalPosition.X / TileSize),
			Mathf.FloorToInt(GlobalPosition.Y / TileSize)
		);

		Vector2I lookingAtTile = playerTilePosition + GetFacingDirectionTileOffset();

		_tilePlaceholder.ShowAtTile(lookingAtTile);

		float targetOpacity = _isMoving ? 0.2f : 0.7f;
		_tilePlaceholder.Modulate = new Color(1, 1, 1, targetOpacity);
	}

	private bool ShouldShowPlaceholder()
	{
		return _currentTool == ToolType.Pickaxe ||
			   _currentTool == ToolType.WateringCan ||
			   _currentTool == ToolType.Hoe ||
			   _currentTool == ToolType.Hammer;
	}

	private Vector2I GetFacingDirectionTileOffset()
	{
		return _lastDirection switch
		{
			"up" => Vector2I.Up,
			"down" => Vector2I.Down,
			"left" => Vector2I.Left,
			"right" => Vector2I.Right,
			_ => Vector2I.Down
		};
	}
}

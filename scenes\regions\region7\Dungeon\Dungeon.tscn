[gd_scene load_steps=36 format=4 uid="uid://cl2q3ccasg5u6"]

[ext_resource type="Texture2D" uid="uid://6f0enfs5248c" path="res://resources/solaria/crypt/tiles/Mesoamerican Dungeon Floors.png" id="1_0nmqi"]
[ext_resource type="Texture2D" uid="uid://cl2ep74lpvb2q" path="res://resources/solaria/crypt/tiles/Mesoamerican Dungeon Animated Liquid.png" id="2_b8vfv"]
[ext_resource type="Texture2D" uid="uid://8eyr15hp0kuq" path="res://resources/solaria/crypt/tiles/Mesoamerican Dungeon Walls.png" id="3_b8vfv"]
[ext_resource type="Texture2D" uid="uid://cig8878wtww6e" path="res://resources/solaria/crypt/torch_1.png" id="3_rfyg2"]
[ext_resource type="PackedScene" uid="uid://c1xjnii3mstjy" path="res://scenes/mapObjects/DungeonPlayer.tscn" id="4_hpnkm"]
[ext_resource type="PackedScene" uid="uid://c13q3mm3n1etg" path="res://scenes/UI/inventory/SelectedToolPanel.tscn" id="5_a7m4y"]
[ext_resource type="Texture2D" uid="uid://d4g1t33libf8y" path="res://resources/solaria/crypt/candle_1.png" id="5_o7aod"]
[ext_resource type="Texture2D" uid="uid://boxwgogbn6ftk" path="res://resources/solaria/crypt/candle_2.png" id="6_k8d7v"]
[ext_resource type="PackedScene" uid="uid://c7pcpuwwc2e7k" path="res://scenes/UI/inventory/InventoryMenu.tscn" id="6_prosc"]
[ext_resource type="Texture2D" uid="uid://ccjg53eohr344" path="res://resources/solaria/crypt/candle_3.png" id="7_24gyc"]
[ext_resource type="PackedScene" uid="uid://bnxwvx42a6onm" path="res://scenes/UI/PlayerStatusPanel.tscn" id="7_rjm1x"]
[ext_resource type="PackedScene" uid="uid://jglbfpyl6t71" path="res://scenes/UI/mapPanel/MapPanel.tscn" id="8_prosc"]
[ext_resource type="Texture2D" uid="uid://ccvmg8duyqrxl" path="res://resources/solaria/crypt/candle_4.png" id="8_y7yu3"]
[ext_resource type="Texture2D" uid="uid://cnscm5ueg62j5" path="res://resources/solaria/crypt/candle_5.png" id="9_o3n45"]
[ext_resource type="PackedScene" uid="uid://bmvnt1kexku0y" path="res://scenes/Portal.tscn" id="9_rjm1x"]
[ext_resource type="PackedScene" uid="uid://c8vfv1kexku0y" path="res://scenes/regions/region7/Dungeon/DungeonManager.tscn" id="10_dungeonmanager"]
[ext_resource type="PackedScene" uid="uid://b47jp40aidjrw" path="res://scenes/MenuManager.tscn" id="11_ngpfh"]
[ext_resource type="PackedScene" path="res://scenes/regions/region7/Dungeon/DungeonDroppedResourceManager.tscn" id="12_droppedresourcemanager"]
[ext_resource type="PackedScene" uid="uid://borl2iygx76kb" path="res://scenes/regions/region7/Dungeon/Quests/DoorForSwitch.tscn" id="13_ilsmu"]
[ext_resource type="PackedScene" uid="uid://but0dxwcbtart" path="res://scenes/regions/region7/Dungeon/Quests/Switch.tscn" id="14_lj2h2"]
[ext_resource type="PackedScene" uid="uid://ct2xtf4ktmakj" path="res://scenes/regions/region7/Dungeon/DungeonGroundTrap.tscn" id="15_lj2h2"]
[ext_resource type="PackedScene" uid="uid://cf1vpvs2pqts5" path="res://scenes/regions/region7/Dungeon/MovingDungeonTrap.tscn" id="16_jogau"]

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_hpnkm"]
texture = ExtResource("1_0nmqi")
0:0/0 = 0
1:0/0 = 0
2:0/0 = 0
3:0/0 = 0
4:0/0 = 0
5:0/0 = 0
6:0/0 = 0
15:0/0 = 0
16:0/0 = 0
17:0/0 = 0
18:0/0 = 0
19:0/0 = 0
24:0/0 = 0
25:0/0 = 0
25:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.359718, -2.87774, 8, -4.85619, 8, 8, -0.359718, 8)
26:0/0 = 0
26:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.719435, 8, -0.899294, 8, 8, -8, 8)
27:0/0 = 0
27:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.719435, 8, -0.899294, 8, 8, -8, 8)
28:0/0 = 0
28:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.719435, -3.95689, -1.07915, -3.59718, -4.67633, 3.77703, -4.85619, 3.77703, -0.899294, 8, -0.899294, 8, 8, -8, 8)
29:0/0 = 0
29:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.719435, 8, -0.899294, 8, 8, -8, 8)
30:0/0 = 0
30:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.719435, 8, -0.899294, 8, 8, -8, 8)
31:0/0 = 0
31:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -5.03605, -0.539577, -5.39576, 0, 8, -8, 8)
32:0/0 = 0
33:0/0 = 0
34:0/0 = 0
35:0/0 = 0
36:0/0 = 0
37:0/0 = 0
0:1/0 = 0
1:1/0 = 0
2:1/0 = 0
3:1/0 = 0
4:1/0 = 0
5:1/0 = 0
15:1/0 = 0
16:1/0 = 0
17:1/0 = 0
18:1/0 = 0
19:1/0 = 0
20:1/0 = 0
21:1/0 = 0
22:1/0 = 0
23:1/0 = 0
24:1/0 = 0
26:1/0 = 0
27:1/0 = 0
29:1/0 = 0
30:1/0 = 0
32:1/0 = 0
33:1/0 = 0
34:1/0 = 0
35:1/0 = 0
36:1/0 = 0
37:1/0 = 0
0:2/0 = 0
1:2/0 = 0
2:2/0 = 0
3:2/0 = 0
4:2/0 = 0
5:2/0 = 0
15:2/0 = 0
16:2/0 = 0
17:2/0 = 0
18:2/0 = 0
19:2/0 = 0
20:2/0 = 0
23:2/0 = 0
24:2/0 = 0
25:2/0 = 0
25:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.359718, -2.87774, 8, -4.85619, 8, 8, -0.359718, 8)
26:2/0 = 0
26:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.719435, 8, -0.899294, 8, 8, -8, 8)
27:2/0 = 0
27:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.719435, 8, -0.899294, 8, 8, -8, 8)
28:2/0 = 0
28:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.719435, -3.95689, -1.07915, -3.59718, -4.67633, 3.77703, -4.85619, 3.77703, -0.899294, 8, -0.899294, 8, 8, -8, 8)
29:2/0 = 0
29:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.719435, 8, -0.899294, 8, 8, -8, 8)
30:2/0 = 0
30:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.719435, 8, -0.899294, 8, 8, -8, 8)
31:2/0 = 0
31:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -5.03605, -0.539577, -5.39576, 0, 8, -8, 8)
32:2/0 = 0
32:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -3.95689, -0.359718, -3.95689, -0.719437, 8, -8, 8)
33:2/0 = 0
33:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-0.179859, -3.95689, 8, -4.13675, 8, 8, 0.899292, 8)
34:2/0 = 0
34:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -3.95689, -0.359718, -3.95689, -0.719437, 8, -8, 8)
35:2/0 = 0
35:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-0.179859, -3.95689, 8, -4.13675, 8, 8, 0.899292, 8)
36:2/0 = 0
36:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -3.95689, -0.359718, -3.95689, -0.719437, 8, -8, 8)
37:2/0 = 0
37:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-0.179859, -3.95689, 8, -4.13675, 8, 8, 0.899292, 8)
0:3/0 = 0
1:3/0 = 0
2:3/0 = 0
3:3/0 = 0
4:3/0 = 0
5:3/0 = 0
15:3/0 = 0
16:3/0 = 0
17:3/0 = 0
18:3/0 = 0
19:3/0 = 0
24:3/0 = 0
26:3/0 = 0
27:3/0 = 0
28:3/0 = 0
29:3/0 = 0
30:3/0 = 0
32:3/0 = 0
32:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
33:3/0 = 0
33:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
34:3/0 = 0
34:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
35:3/0 = 0
35:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
36:3/0 = 0
36:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
37:3/0 = 0
37:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
0:4/0 = 0
1:4/0 = 0
2:4/0 = 0
3:4/0 = 0
4:4/0 = 0
5:4/0 = 0
6:4/0 = 0
7:4/0 = 0
8:4/0 = 0
9:4/0 = 0
10:4/0 = 0
11:4/0 = 0
12:4/0 = 0
13:4/0 = 0
15:4/0 = 0
16:4/0 = 0
17:4/0 = 0
18:4/0 = 0
19:4/0 = 0
20:4/0 = 0
21:4/0 = 0
22:4/0 = 0
23:4/0 = 0
24:4/0 = 0
25:4/0 = 0
25:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.359718, -2.87774, 8, -4.85619, 8, 8, -0.359718, 8)
26:4/0 = 0
26:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.719435, 8, -0.899294, 8, 8, -8, 8)
27:4/0 = 0
27:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.719435, 8, -0.899294, 8, 8, -8, 8)
28:4/0 = 0
28:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.719435, -3.95689, -1.07915, -3.59718, -4.67633, 3.77703, -4.85619, 3.77703, -0.899294, 8, -0.899294, 8, 8, -8, 8)
29:4/0 = 0
29:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.719435, 8, -0.899294, 8, 8, -8, 8)
30:4/0 = 0
30:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.719435, 8, -0.899294, 8, 8, -8, 8)
31:4/0 = 0
31:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -5.03605, -0.539577, -5.39576, 0, 8, -8, 8)
32:4/0 = 0
32:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
33:4/0 = 0
33:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
34:4/0 = 0
34:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
35:4/0 = 0
35:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
36:4/0 = 0
36:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
37:4/0 = 0
37:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
0:5/0 = 0
1:5/0 = 0
2:5/0 = 0
3:5/0 = 0
4:5/0 = 0
5:5/0 = 0
6:5/0 = 0
7:5/0 = 0
8:5/0 = 0
9:5/0 = 0
11:5/0 = 0
12:5/0 = 0
13:5/0 = 0
15:5/0 = 0
16:5/0 = 0
17:5/0 = 0
18:5/0 = 0
19:5/0 = 0
20:5/0 = 0
23:5/0 = 0
24:5/0 = 0
26:5/0 = 0
27:5/0 = 0
29:5/0 = 0
30:5/0 = 0
32:5/0 = 0
32:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
33:5/0 = 0
33:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
34:5/0 = 0
34:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
35:5/0 = 0
35:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
36:5/0 = 0
36:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
37:5/0 = 0
37:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
0:6/0 = 0
1:6/0 = 0
2:6/0 = 0
3:6/0 = 0
4:6/0 = 0
5:6/0 = 0
6:6/0 = 0
7:6/0 = 0
8:6/0 = 0
9:6/0 = 0
10:6/0 = 0
11:6/0 = 0
15:6/0 = 0
16:6/0 = 0
17:6/0 = 0
18:6/0 = 0
19:6/0 = 0
19:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, 4.4033, 4.54199, -8, 5.30476, -8, -8, 5.09673)
24:6/0 = 0
26:6/0 = 0
27:6/0 = 0
28:6/0 = 0
29:6/0 = 0
30:6/0 = 0
34:6/0 = 0
34:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
35:6/0 = 0
35:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
36:6/0 = 0
36:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
37:6/0 = 0
37:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
0:7/0 = 0
1:7/0 = 0
2:7/0 = 0
3:7/0 = 0
4:7/0 = 0
5:7/0 = 0
8:7/0 = 0
9:7/0 = 0
10:7/0 = 0
11:7/0 = 0
12:7/0 = 0
13:7/0 = 0
15:7/0 = 0
16:7/0 = 0
17:7/0 = 0
18:7/0 = 0
19:7/0 = 0
20:7/0 = 0
20:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(8, -4.6148, 8, 8, -3.60793, 8)
21:7/0 = 0
21:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -4.90524, -8, -8, -3.86473)
22:7/0 = 0
22:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(3.27016, -8, 8, -8, 8, -4.01338)
23:7/0 = 0
23:7/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -5.05388, 4.31067, 8, -8, 8)
24:7/0 = 0
26:7/0 = 0
27:7/0 = 0
28:7/0 = 0
30:7/0 = 0
0:8/0 = 0
1:8/0 = 0
2:8/0 = 0
3:8/0 = 0
4:8/0 = 0
5:8/0 = 0
6:8/0 = 0
7:8/0 = 0
8:8/0 = 0
9:8/0 = 0
11:8/0 = 0
12:8/0 = 0
13:8/0 = 0
15:8/0 = 0
16:8/0 = 0
17:8/0 = 0
18:8/0 = 0
19:8/0 = 0
19:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(8, -4.6148, 8, 8, -3.60793, 8)
20:8/0 = 0
23:8/0 = 0
24:8/0 = 0
24:8/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -5.05388, 4.31067, 8, -8, 8)
28:8/0 = 0
29:8/0 = 0
30:8/0 = 0
0:9/0 = 0
1:9/0 = 0
2:9/0 = 0
3:9/0 = 0
4:9/0 = 0
5:9/0 = 0
6:9/0 = 0
7:9/0 = 0
8:9/0 = 0
9:9/0 = 0
10:9/0 = 0
11:9/0 = 0
12:9/0 = 0
13:9/0 = 0
14:9/0 = 0
15:9/0 = 0
16:9/0 = 0
17:9/0 = 0
18:9/0 = 0
19:9/0 = 0
19:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, 4.4033, 4.54199, -8, 5.30476, -8, -8, 5.09673)
24:9/0 = 0
0:10/0 = 0
1:10/0 = 0
2:10/0 = 0
3:10/0 = 0
4:10/0 = 0
12:10/0 = 0
13:10/0 = 0
14:10/0 = 0
19:10/0 = 0
20:10/0 = 0
21:10/0 = 0
22:10/0 = 0
23:10/0 = 0
24:10/0 = 0
25:10/0 = 0
26:10/0 = 0
27:10/0 = 0
28:10/0 = 0
29:10/0 = 0
30:10/0 = 0
31:10/0 = 0
32:10/0 = 0
33:10/0 = 0
34:10/0 = 0
35:10/0 = 0
36:10/0 = 0
37:10/0 = 0
0:11/0 = 0
1:11/0 = 0
2:11/0 = 0
3:11/0 = 0
4:11/0 = 0
5:11/0 = 0
6:11/0 = 0
7:11/0 = 0
12:11/0 = 0
13:11/0 = 0
14:11/0 = 0
19:11/0 = 0
20:11/0 = 0
23:11/0 = 0
24:11/0 = 0
26:11/0 = 0
27:11/0 = 0
29:11/0 = 0
30:11/0 = 0
32:11/0 = 0
33:11/0 = 0
34:11/0 = 0
35:11/0 = 0
36:11/0 = 0
37:11/0 = 0
0:12/0 = 0
1:12/0 = 0
2:12/0 = 0
3:12/0 = 0
4:12/0 = 0
4:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -8, -6.93433, 6.969, -6.93433, 7.03834, 8, 8, 8, 8, -8)
5:12/0 = 0
5:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -8, -6.93433, -8, 8, -6.969, 8, -6.89966, -7.00367, 8, -6.93433, 8, -8)
6:12/0 = 0
7:12/0 = 0
8:12/0 = 0
9:12/0 = 0
10:12/0 = 0
11:12/0 = 0
12:12/0 = 0
13:12/0 = 0
19:12/0 = 0
24:12/0 = 0
25:12/0 = 0
26:12/0 = 0
27:12/0 = 0
28:12/0 = 0
29:12/0 = 0
30:12/0 = 0
31:12/0 = 0
32:12/0 = 0
32:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -3.95689, -0.359718, -3.95689, -0.719437, 8, -8, 8)
33:12/0 = 0
33:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-0.179859, -3.95689, 8, -4.13675, 8, 8, 0.899292, 8)
34:12/0 = 0
34:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -3.95689, -0.359718, -3.95689, -0.719437, 8, -8, 8)
35:12/0 = 0
35:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-0.179859, -3.95689, 8, -4.13675, 8, 8, 0.899292, 8)
36:12/0 = 0
36:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -3.95689, -0.359718, -3.95689, -0.719437, 8, -8, 8)
37:12/0 = 0
37:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-0.179859, -3.95689, 8, -4.13675, 8, 8, 0.899292, 8)
0:13/0 = 0
1:13/0 = 0
2:13/0 = 0
3:13/0 = 0
4:13/0 = 0
4:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(8, -8, 8, 8, -8, 8, -8, 7.14236, 7.03834, 7.00367, 7.10769, -8)
5:13/0 = 0
5:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(8, 8, -8.0785, 9.08397, -8, 7.14236, -7.03835, 7.07302, 8, 7.00367)
6:13/0 = 0
7:13/0 = 0
8:13/0 = 0
9:13/0 = 0
11:13/0 = 0
12:13/0 = 0
13:13/0 = 0
19:13/0 = 0
20:13/0 = 0
23:13/0 = 0
24:13/0 = 0
26:13/0 = 0
27:13/0 = 0
28:13/0 = 0
29:13/0 = 0
30:13/0 = 0
32:13/0 = 0
32:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
33:13/0 = 0
33:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
34:13/0 = 0
34:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
35:13/0 = 0
35:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
36:13/0 = 0
36:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
37:13/0 = 0
37:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
0:14/0 = 0
2:14/0 = 0
2:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, 7.35039, 8, 7.14236, 8, 8, -8, 8)
3:14/0 = 0
3:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, -7.07302, -8, -7.00367)
4:14/0 = 0
5:14/0 = 0
5:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(8, -8, 8, 8, 7.03834, 8, 7.10769, -8)
6:14/0 = 0
7:14/0 = 0
8:14/0 = 0
9:14/0 = 0
10:14/0 = 0
11:14/0 = 0
19:14/0 = 0
24:14/0 = 0
25:14/0 = 0
26:14/0 = 0
27:14/0 = 0
28:14/0 = 0
29:14/0 = 0
30:14/0 = 0
31:14/0 = 0
32:14/0 = 0
32:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
33:14/0 = 0
33:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
34:14/0 = 0
34:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
35:14/0 = 0
35:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
36:14/0 = 0
36:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
37:14/0 = 0
37:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
0:15/0 = 0
1:15/0 = 0
2:15/0 = 0
3:15/0 = 0
4:15/0 = 0
5:15/0 = 0
6:15/0 = 0
7:15/0 = 0
8:15/0 = 0
9:15/0 = 0
10:15/0 = 0
11:15/0 = 0
12:15/0 = 0
13:15/0 = 0
19:15/0 = 0
24:15/0 = 0
26:15/0 = 0
27:15/0 = 0
29:15/0 = 0
30:15/0 = 0
32:15/0 = 0
32:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
33:15/0 = 0
33:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
34:15/0 = 0
34:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
35:15/0 = 0
35:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
36:15/0 = 0
36:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
37:15/0 = 0
37:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
0:16/0 = 0
1:16/0 = 0
2:16/0 = 0
3:16/0 = 0
4:16/0 = 0
5:16/0 = 0
6:16/0 = 0
7:16/0 = 0
8:16/0 = 0
9:16/0 = 0
11:16/0 = 0
12:16/0 = 0
13:16/0 = 0
19:16/0 = 0
20:16/0 = 0
23:16/0 = 0
24:16/0 = 0
26:16/0 = 0
27:16/0 = 0
28:16/0 = 0
29:16/0 = 0
30:16/0 = 0
34:16/0 = 0
34:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
35:16/0 = 0
35:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
36:16/0 = 0
36:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -1.07915, -8, -0.719437, 8, -8, 8)
37:16/0 = 0
37:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899292, -8, 8, -8, 8, 8, 0.899292, 8)
0:17/0 = 0
1:17/0 = 0
2:17/0 = 0
3:17/0 = 0
4:17/0 = 0
5:17/0 = 0
6:17/0 = 0
7:17/0 = 0
8:17/0 = 0
9:17/0 = 0
10:17/0 = 0
11:17/0 = 0
12:17/0 = 0
13:17/0 = 0
14:17/0 = 0
19:17/0 = 0
24:17/0 = 0
26:17/0 = 0
27:17/0 = 0
28:17/0 = 0
30:17/0 = 0
0:18/0 = 0
1:18/0 = 0
2:18/0 = 0
3:18/0 = 0
12:18/0 = 0
13:18/0 = 0
14:18/0 = 0
28:18/0 = 0
29:18/0 = 0
30:18/0 = 0
0:19/0 = 0
1:19/0 = 0
2:19/0 = 0
3:19/0 = 0
4:19/0 = 0
5:19/0 = 0
6:19/0 = 0
7:19/0 = 0
12:19/0 = 0
13:19/0 = 0
14:19/0 = 0

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_iyje8"]
texture = ExtResource("2_b8vfv")
0:0/animation_speed = 4.0
0:0/animation_frame_0/duration = 1.0
0:0/animation_frame_1/duration = 1.0
0:0/animation_frame_2/duration = 1.0
0:0/animation_frame_3/duration = 1.0
0:0/0 = 0
0:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:1/animation_separation = Vector2i(2, 0)
0:1/animation_speed = 4.0
0:1/animation_frame_0/duration = 1.0
0:1/animation_frame_1/duration = 1.0
0:1/animation_frame_2/duration = 1.0
0:1/animation_frame_3/duration = 1.0
0:1/0 = 0
0:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:2/animation_separation = Vector2i(2, 0)
0:2/animation_speed = 4.0
0:2/animation_frame_0/duration = 1.0
0:2/animation_frame_1/duration = 1.0
0:2/animation_frame_2/duration = 1.0
0:2/animation_frame_3/duration = 1.0
0:2/0 = 0
0:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:3/animation_separation = Vector2i(2, 0)
0:3/animation_speed = 4.0
0:3/animation_frame_0/duration = 1.0
0:3/animation_frame_1/duration = 1.0
0:3/animation_frame_2/duration = 1.0
0:3/animation_frame_3/duration = 1.0
0:3/0 = 0
0:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:3/animation_separation = Vector2i(2, 0)
1:3/animation_speed = 4.0
1:3/animation_frame_0/duration = 1.0
1:3/animation_frame_1/duration = 1.0
1:3/animation_frame_2/duration = 1.0
1:3/animation_frame_3/duration = 1.0
1:3/0 = 0
1:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:3/animation_separation = Vector2i(2, 0)
2:3/animation_speed = 4.0
2:3/animation_frame_0/duration = 1.0
2:3/animation_frame_1/duration = 1.0
2:3/animation_frame_2/duration = 1.0
2:3/animation_frame_3/duration = 1.0
2:3/0 = 0
2:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:2/animation_separation = Vector2i(2, 0)
2:2/animation_speed = 4.0
2:2/animation_frame_0/duration = 1.0
2:2/animation_frame_1/duration = 1.0
2:2/animation_frame_2/duration = 1.0
2:2/animation_frame_3/duration = 1.0
2:2/0 = 0
2:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:1/animation_separation = Vector2i(2, 0)
2:1/animation_speed = 4.0
2:1/animation_frame_0/duration = 1.0
2:1/animation_frame_1/duration = 1.0
2:1/animation_frame_2/duration = 1.0
2:1/animation_frame_3/duration = 1.0
2:1/0 = 0
2:1/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:4/animation_separation = Vector2i(1, 0)
0:4/animation_speed = 4.0
0:4/animation_frame_0/duration = 1.0
0:4/animation_frame_1/duration = 1.0
0:4/animation_frame_2/duration = 1.0
0:4/animation_frame_3/duration = 1.0
0:4/0 = 0
1:4/animation_separation = Vector2i(1, 0)
1:4/animation_speed = 4.0
1:4/animation_frame_0/duration = 1.0
1:4/animation_frame_1/duration = 1.0
1:4/animation_frame_2/duration = 1.0
1:4/animation_frame_3/duration = 1.0
1:4/0 = 0
0:5/animation_separation = Vector2i(2, 0)
0:5/animation_speed = 4.0
0:5/animation_frame_0/duration = 1.0
0:5/animation_frame_1/duration = 1.0
0:5/animation_frame_2/duration = 1.0
0:5/animation_frame_3/duration = 1.0
0:5/0 = 0
0:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
1:5/animation_separation = Vector2i(2, 0)
1:5/animation_speed = 4.0
1:5/animation_frame_0/duration = 1.0
1:5/animation_frame_1/duration = 1.0
1:5/animation_frame_2/duration = 1.0
1:5/animation_frame_3/duration = 1.0
1:5/0 = 0
1:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:5/animation_separation = Vector2i(2, 0)
2:5/animation_speed = 4.0
2:5/animation_frame_0/duration = 1.0
2:5/animation_frame_1/duration = 1.0
2:5/animation_frame_2/duration = 1.0
2:5/animation_frame_3/duration = 1.0
2:5/0 = 0
2:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
0:6/animation_separation = Vector2i(2, 0)
0:6/animation_speed = 4.0
0:6/animation_frame_0/duration = 1.0
0:6/animation_frame_1/duration = 1.0
0:6/animation_frame_2/duration = 1.0
0:6/animation_frame_3/duration = 1.0
0:6/0 = 0
1:6/animation_separation = Vector2i(2, 0)
1:6/animation_speed = 4.0
1:6/animation_frame_0/duration = 1.0
1:6/animation_frame_1/duration = 1.0
1:6/animation_frame_2/duration = 1.0
1:6/animation_frame_3/duration = 1.0
1:6/0 = 0
2:6/animation_separation = Vector2i(2, 0)
2:6/animation_speed = 4.0
2:6/animation_frame_0/duration = 1.0
2:6/animation_frame_1/duration = 1.0
2:6/animation_frame_2/duration = 1.0
2:6/animation_frame_3/duration = 1.0
2:6/0 = 0
0:7/animation_speed = 4.0
0:7/animation_frame_0/duration = 1.0
0:7/animation_frame_1/duration = 1.0
0:7/animation_frame_2/duration = 1.0
0:7/animation_frame_3/duration = 1.0
0:7/0 = 0
4:7/animation_speed = 4.0
4:7/animation_frame_0/duration = 1.0
4:7/animation_frame_1/duration = 1.0
4:7/animation_frame_2/duration = 1.0
4:7/animation_frame_3/duration = 1.0
4:7/0 = 0
8:7/animation_speed = 4.0
8:7/animation_frame_0/duration = 1.0
8:7/animation_frame_1/duration = 1.0
8:7/animation_frame_2/duration = 1.0
8:7/animation_frame_3/duration = 1.0
8:7/0 = 0
8:8/animation_speed = 4.0
8:8/animation_frame_0/duration = 1.0
8:8/animation_frame_1/duration = 1.0
8:8/animation_frame_2/duration = 1.0
8:8/animation_frame_3/duration = 1.0
8:8/0 = 0
8:9/animation_speed = 4.0
8:9/animation_frame_0/duration = 1.0
8:9/animation_frame_1/duration = 1.0
8:9/animation_frame_2/duration = 1.0
8:9/animation_frame_3/duration = 1.0
8:9/0 = 0
8:10/animation_speed = 4.0
8:10/animation_frame_0/duration = 1.0
8:10/animation_frame_1/duration = 1.0
8:10/animation_frame_2/duration = 1.0
8:10/animation_frame_3/duration = 1.0
8:10/0 = 0
8:11/animation_speed = 4.0
8:11/animation_frame_0/duration = 1.0
8:11/animation_frame_1/duration = 1.0
8:11/animation_frame_2/duration = 1.0
8:11/animation_frame_3/duration = 1.0
8:11/0 = 0
8:12/animation_speed = 4.0
8:12/animation_frame_0/duration = 1.0
8:12/animation_frame_1/duration = 1.0
8:12/animation_frame_2/duration = 1.0
8:12/animation_frame_3/duration = 1.0
8:12/0 = 0
4:12/animation_speed = 4.0
4:12/animation_frame_0/duration = 1.0
4:12/animation_frame_1/duration = 1.0
4:12/animation_frame_2/duration = 1.0
4:12/animation_frame_3/duration = 1.0
4:12/0 = 0
4:11/animation_speed = 4.0
4:11/animation_frame_0/duration = 1.0
4:11/animation_frame_1/duration = 1.0
4:11/animation_frame_2/duration = 1.0
4:11/animation_frame_3/duration = 1.0
4:11/0 = 0
4:10/animation_speed = 4.0
4:10/animation_frame_0/duration = 1.0
4:10/animation_frame_1/duration = 1.0
4:10/animation_frame_2/duration = 1.0
4:10/animation_frame_3/duration = 1.0
4:10/0 = 0
4:9/animation_speed = 4.0
4:9/animation_frame_0/duration = 1.0
4:9/animation_frame_1/duration = 1.0
4:9/animation_frame_2/duration = 1.0
4:9/animation_frame_3/duration = 1.0
4:9/0 = 0
4:8/animation_speed = 4.0
4:8/animation_frame_0/duration = 1.0
4:8/animation_frame_1/duration = 1.0
4:8/animation_frame_2/duration = 1.0
4:8/animation_frame_3/duration = 1.0
4:8/0 = 0
0:8/animation_speed = 4.0
0:8/animation_frame_0/duration = 1.0
0:8/animation_frame_1/duration = 1.0
0:8/animation_frame_2/duration = 1.0
0:8/animation_frame_3/duration = 1.0
0:8/0 = 0
0:9/animation_speed = 4.0
0:9/animation_frame_0/duration = 1.0
0:9/animation_frame_1/duration = 1.0
0:9/animation_frame_2/duration = 1.0
0:9/animation_frame_3/duration = 1.0
0:9/0 = 0
0:10/animation_speed = 4.0
0:10/animation_frame_0/duration = 1.0
0:10/animation_frame_1/duration = 1.0
0:10/animation_frame_2/duration = 1.0
0:10/animation_frame_3/duration = 1.0
0:10/0 = 0
0:11/animation_speed = 4.0
0:11/animation_frame_0/duration = 1.0
0:11/animation_frame_1/duration = 1.0
0:11/animation_frame_2/duration = 1.0
0:11/animation_frame_3/duration = 1.0
0:11/0 = 0
0:12/animation_speed = 4.0
0:12/animation_frame_0/duration = 1.0
0:12/animation_frame_1/duration = 1.0
0:12/animation_frame_2/duration = 1.0
0:12/animation_frame_3/duration = 1.0
0:12/0 = 0
0:13/animation_speed = 4.0
0:13/animation_frame_0/duration = 1.0
0:13/animation_frame_1/duration = 1.0
0:13/animation_frame_2/duration = 1.0
0:13/animation_frame_3/duration = 1.0
0:13/0 = 0
0:14/animation_speed = 4.0
0:14/animation_frame_0/duration = 1.0
0:14/animation_frame_1/duration = 1.0
0:14/animation_frame_2/duration = 1.0
0:14/animation_frame_3/duration = 1.0
0:14/0 = 0
0:15/animation_speed = 4.0
0:15/animation_frame_0/duration = 1.0
0:15/animation_frame_1/duration = 1.0
0:15/animation_frame_2/duration = 1.0
0:15/animation_frame_3/duration = 1.0
0:15/0 = 0
0:16/animation_speed = 4.0
0:16/animation_frame_0/duration = 1.0
0:16/animation_frame_1/duration = 1.0
0:16/animation_frame_2/duration = 1.0
0:16/animation_frame_3/duration = 1.0
0:16/0 = 0
0:17/animation_speed = 4.0
0:17/animation_frame_0/duration = 1.0
0:17/animation_frame_1/duration = 1.0
0:17/animation_frame_2/duration = 1.0
0:17/animation_frame_3/duration = 1.0
0:17/0 = 0
0:18/animation_speed = 4.0
0:18/animation_frame_0/duration = 1.0
0:18/animation_frame_1/duration = 1.0
0:18/animation_frame_2/duration = 1.0
0:18/animation_frame_3/duration = 1.0
0:18/0 = 0
0:19/animation_speed = 4.0
0:19/animation_frame_0/duration = 1.0
0:19/animation_frame_1/duration = 1.0
0:19/animation_frame_2/duration = 1.0
0:19/animation_frame_3/duration = 1.0
0:19/0 = 0
0:20/animation_speed = 4.0
0:20/animation_frame_0/duration = 1.0
0:20/animation_frame_1/duration = 1.0
0:20/animation_frame_2/duration = 1.0
0:20/animation_frame_3/duration = 1.0
0:20/0 = 0
0:21/animation_speed = 4.0
0:21/animation_frame_0/duration = 1.0
0:21/animation_frame_1/duration = 1.0
0:21/animation_frame_2/duration = 1.0
0:21/animation_frame_3/duration = 1.0
0:21/0 = 0
4:14/animation_speed = 4.0
4:14/animation_frame_0/duration = 1.0
4:14/animation_frame_1/duration = 1.0
4:14/animation_frame_2/duration = 1.0
4:14/animation_frame_3/duration = 1.0
4:14/0 = 0
4:15/animation_speed = 4.0
4:15/animation_frame_0/duration = 1.0
4:15/animation_frame_1/duration = 1.0
4:15/animation_frame_2/duration = 1.0
4:15/animation_frame_3/duration = 1.0
4:15/0 = 0
4:16/animation_speed = 4.0
4:16/animation_frame_0/duration = 1.0
4:16/animation_frame_1/duration = 1.0
4:16/animation_frame_2/duration = 1.0
4:16/animation_frame_3/duration = 1.0
4:16/0 = 0
4:17/animation_speed = 4.0
4:17/animation_frame_0/duration = 1.0
4:17/animation_frame_1/duration = 1.0
4:17/animation_frame_2/duration = 1.0
4:17/animation_frame_3/duration = 1.0
4:17/0 = 0
4:18/animation_speed = 4.0
4:18/animation_frame_0/duration = 1.0
4:18/animation_frame_1/duration = 1.0
4:18/animation_frame_2/duration = 1.0
4:18/animation_frame_3/duration = 1.0
4:18/0 = 0
4:19/animation_speed = 4.0
4:19/animation_frame_0/duration = 1.0
4:19/animation_frame_1/duration = 1.0
4:19/animation_frame_2/duration = 1.0
4:19/animation_frame_3/duration = 1.0
4:19/0 = 0
8:19/animation_speed = 4.0
8:19/animation_frame_0/duration = 1.0
8:19/animation_frame_1/duration = 1.0
8:19/animation_frame_2/duration = 1.0
8:19/animation_frame_3/duration = 1.0
8:19/0 = 0
8:18/animation_speed = 4.0
8:18/animation_frame_0/duration = 1.0
8:18/animation_frame_1/duration = 1.0
8:18/animation_frame_2/duration = 1.0
8:18/animation_frame_3/duration = 1.0
8:18/0 = 0
8:17/animation_speed = 4.0
8:17/animation_frame_0/duration = 1.0
8:17/animation_frame_1/duration = 1.0
8:17/animation_frame_2/duration = 1.0
8:17/animation_frame_3/duration = 1.0
8:17/0 = 0
8:16/animation_speed = 4.0
8:16/animation_frame_0/duration = 1.0
8:16/animation_frame_1/duration = 1.0
8:16/animation_frame_2/duration = 1.0
8:16/animation_frame_3/duration = 1.0
8:16/0 = 0
8:15/animation_speed = 4.0
8:15/animation_frame_0/duration = 1.0
8:15/animation_frame_1/duration = 1.0
8:15/animation_frame_2/duration = 1.0
8:15/animation_frame_3/duration = 1.0
8:15/0 = 0
8:14/animation_speed = 4.0
8:14/animation_frame_0/duration = 1.0
8:14/animation_frame_1/duration = 1.0
8:14/animation_frame_2/duration = 1.0
8:14/animation_frame_3/duration = 1.0
8:14/0 = 0
4:13/0 = 0
4:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)

[sub_resource type="TileSet" id="TileSet_e2y05"]
physics_layer_0/collision_layer = 1
physics_layer_0/collision_priority = 2.0
terrain_set_0/mode = 1
terrain_set_0/terrain_0/name = "Terrain 0"
terrain_set_0/terrain_0/color = Color(0.5, 0.34375, 0.25, 1)
terrain_set_0/terrain_1/name = "Terrain 1"
terrain_set_0/terrain_1/color = Color(0.5, 0.4375, 0.25, 1)
sources/0 = SubResource("TileSetAtlasSource_hpnkm")
sources/1 = SubResource("TileSetAtlasSource_iyje8")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_24pqw"]
texture = ExtResource("3_rfyg2")
0:0/animation_speed = 4.0
0:0/animation_frame_0/duration = 1.0
0:0/animation_frame_1/duration = 1.0
0:0/animation_frame_2/duration = 1.0
0:0/animation_frame_3/duration = 1.0
0:0/0 = 0

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_m55mf"]
texture = ExtResource("3_b8vfv")
1:0/0 = 0
2:0/0 = 0
3:0/0 = 0
5:0/0 = 0
6:0/0 = 0
7:0/0 = 0
9:0/0 = 0
10:0/0 = 0
11:0/0 = 0
12:0/0 = 0
13:0/0 = 0
14:0/0 = 0
15:0/0 = 0
17:0/0 = 0
20:0/0 = 0
27:0/0 = 0
28:0/0 = 0
29:0/0 = 0
30:0/0 = 0
31:0/0 = 0
32:0/0 = 0
0:1/0 = 0
1:1/0 = 0
2:1/0 = 0
3:1/0 = 0
4:1/0 = 0
5:1/0 = 0
6:1/0 = 0
7:1/0 = 0
8:1/0 = 0
9:1/0 = 0
10:1/0 = 0
11:1/0 = 0
12:1/0 = 0
13:1/0 = 0
14:1/0 = 0
15:1/0 = 0
17:1/0 = 0
18:1/0 = 0
20:1/0 = 0
21:1/0 = 0
24:1/0 = 0
27:1/0 = 0
28:1/0 = 0
29:1/0 = 0
30:1/0 = 0
31:1/0 = 0
32:1/0 = 0
0:2/0 = 0
1:2/0 = 0
2:2/0 = 0
3:2/0 = 0
4:2/0 = 0
5:2/0 = 0
7:2/0 = 0
8:2/0 = 0
9:2/0 = 0
11:2/0 = 0
12:2/0 = 0
13:2/0 = 0
17:2/0 = 0
18:2/0 = 0
19:2/0 = 0
20:2/0 = 0
21:2/0 = 0
22:2/0 = 0
23:2/0 = 0
24:2/0 = 0
30:2/0 = 0
31:2/0 = 0
32:2/0 = 0
0:3/0 = 0
1:3/0 = 0
2:3/0 = 0
3:3/0 = 0
4:3/0 = 0
5:3/0 = 0
6:3/0 = 0
7:3/0 = 0
8:3/0 = 0
9:3/0 = 0
10:3/0 = 0
11:3/0 = 0
13:3/0 = 0
14:3/0 = 0
15:3/0 = 0
30:3/0 = 0
31:3/0 = 0
32:3/0 = 0
0:4/0 = 0
1:4/0 = 0
1:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:4/0 = 0
3:4/0 = 0
3:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:4/0 = 0
5:4/0 = 0
5:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:4/0 = 0
7:4/0 = 0
7:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:4/0 = 0
9:4/0 = 0
9:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:4/0 = 0
11:4/0 = 0
11:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:4/0 = 0
13:4/0 = 0
14:4/0 = 0
15:4/0 = 0
17:4/0 = 0
18:4/0 = 0
19:4/0 = 0
20:4/0 = 0
21:4/0 = 0
22:4/0 = 0
23:4/0 = 0
24:4/0 = 0
25:4/0 = 0
27:4/0 = 0
28:4/0 = 0
29:4/0 = 0
30:4/0 = 0
31:4/0 = 0
32:4/0 = 0
0:5/0 = 0
1:5/0 = 0
1:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:5/0 = 0
3:5/0 = 0
3:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:5/0 = 0
5:5/0 = 0
5:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:5/0 = 0
7:5/0 = 0
7:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:5/0 = 0
9:5/0 = 0
9:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:5/0 = 0
11:5/0 = 0
11:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:5/0 = 0
13:5/0 = 0
14:5/0 = 0
15:5/0 = 0
17:5/0 = 0
18:5/0 = 0
19:5/0 = 0
20:5/0 = 0
21:5/0 = 0
22:5/0 = 0
23:5/0 = 0
24:5/0 = 0
25:5/0 = 0
27:5/0 = 0
28:5/0 = 0
29:5/0 = 0
30:5/0 = 0
31:5/0 = 0
32:5/0 = 0
0:6/0 = 0
1:6/0 = 0
1:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
2:6/0 = 0
2:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
3:6/0 = 0
3:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
4:6/0 = 0
5:6/0 = 0
5:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
6:6/0 = 0
6:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
7:6/0 = 0
7:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
8:6/0 = 0
9:6/0 = 0
9:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
10:6/0 = 0
10:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
11:6/0 = 0
11:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
12:6/0 = 0
13:6/0 = 0
15:6/0 = 0
17:6/0 = 0
18:6/0 = 0
19:6/0 = 0
20:6/0 = 0
21:6/0 = 0
22:6/0 = 0
23:6/0 = 0
24:6/0 = 0
25:6/0 = 0
30:6/0 = 0
31:6/0 = 0
32:6/0 = 0
0:7/0 = 0
1:7/0 = 0
2:7/0 = 0
3:7/0 = 0
4:7/0 = 0
5:7/0 = 0
6:7/0 = 0
7:7/0 = 0
8:7/0 = 0
9:7/0 = 0
10:7/0 = 0
11:7/0 = 0
17:7/0 = 0
18:7/0 = 0
19:7/0 = 0
20:7/0 = 0
21:7/0 = 0
22:7/0 = 0
23:7/0 = 0
24:7/0 = 0
25:7/0 = 0
26:7/0 = 0
30:7/0 = 0
31:7/0 = 0
32:7/0 = 0
1:8/0 = 0
2:8/0 = 0
3:8/0 = 0
5:8/0 = 0
6:8/0 = 0
7:8/0 = 0
8:8/0 = 0
9:8/0 = 0
10:8/0 = 0
11:8/0 = 0
17:8/0 = 0
18:8/0 = 0
19:8/0 = 0
20:8/0 = 0
21:8/0 = 0
22:8/0 = 0
23:8/0 = 0
24:8/0 = 0
25:8/0 = 0
26:8/0 = 0
1:9/0 = 0
2:9/0 = 0
3:9/0 = 0
5:9/0 = 0
6:9/0 = 0
7:9/0 = 0
9:9/0 = 0
10:9/0 = 0
11:9/0 = 0
17:9/0 = 0
17:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
18:9/0 = 0
18:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
19:9/0 = 0
19:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
20:9/0 = 0
20:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
21:9/0 = 0
21:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
22:9/0 = 0
22:9/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, 8, -8, 8, 8, -8, 8)
23:9/0 = 0
24:9/0 = 0
25:9/0 = 0
26:9/0 = 0
5:10/0 = 0
6:10/0 = 0
7:10/0 = 0
9:10/0 = 0
10:10/0 = 0
11:10/0 = 0
17:10/0 = 0
18:10/0 = 0
19:10/0 = 0
20:10/0 = 0
21:10/0 = 0
22:10/0 = 0
23:10/0 = 0
24:10/0 = 0
25:10/0 = 0
11:11/0 = 0
17:11/0 = 0
18:11/0 = 0
19:11/0 = 0
20:11/0 = 0
21:11/0 = 0
22:11/0 = 0
23:11/0 = 0
24:11/0 = 0
25:11/0 = 0
1:12/0 = 0
2:12/0 = 0
3:12/0 = 0
5:12/0 = 0
6:12/0 = 0
7:12/0 = 0
9:12/0 = 0
10:12/0 = 0
11:12/0 = 0
12:12/0 = 0
13:12/0 = 0
14:12/0 = 0
15:12/0 = 0
17:12/0 = 0
18:12/0 = 0
19:12/0 = 0
20:12/0 = 0
21:12/0 = 0
22:12/0 = 0
23:12/0 = 0
24:12/0 = 0
25:12/0 = 0
0:13/0 = 0
1:13/0 = 0
2:13/0 = 0
3:13/0 = 0
4:13/0 = 0
5:13/0 = 0
6:13/0 = 0
7:13/0 = 0
8:13/0 = 0
9:13/0 = 0
10:13/0 = 0
11:13/0 = 0
12:13/0 = 0
13:13/0 = 0
14:13/0 = 0
15:13/0 = 0
17:13/0 = 0
18:13/0 = 0
19:13/0 = 0
20:13/0 = 0
21:13/0 = 0
22:13/0 = 0
23:13/0 = 0
24:13/0 = 0
25:13/0 = 0
26:13/0 = 0
27:13/0 = 0
28:13/0 = 0
29:13/0 = 0
30:13/0 = 0
0:14/0 = 0
1:14/0 = 0
2:14/0 = 0
3:14/0 = 0
4:14/0 = 0
5:14/0 = 0
7:14/0 = 0
8:14/0 = 0
9:14/0 = 0
11:14/0 = 0
12:14/0 = 0
13:14/0 = 0
17:14/0 = 0
18:14/0 = 0
19:14/0 = 0
20:14/0 = 0
21:14/0 = 0
22:14/0 = 0
23:14/0 = 0
24:14/0 = 0
25:14/0 = 0
26:14/0 = 0
27:14/0 = 0
28:14/0 = 0
29:14/0 = 0
30:14/0 = 0
0:15/0 = 0
1:15/0 = 0
2:15/0 = 0
3:15/0 = 0
4:15/0 = 0
5:15/0 = 0
6:15/0 = 0
7:15/0 = 0
8:15/0 = 0
9:15/0 = 0
10:15/0 = 0
11:15/0 = 0
13:15/0 = 0
14:15/0 = 0
15:15/0 = 0
17:15/0 = 0
19:15/0 = 0
20:15/0 = 0
23:15/0 = 0
24:15/0 = 0
26:15/0 = 0
27:15/0 = 0
30:15/0 = 0
0:16/0 = 0
1:16/0 = 0
2:16/0 = 0
3:16/0 = 0
4:16/0 = 0
5:16/0 = 0
6:16/0 = 0
7:16/0 = 0
8:16/0 = 0
9:16/0 = 0
10:16/0 = 0
11:16/0 = 0
12:16/0 = 0
13:16/0 = 0
14:16/0 = 0
15:16/0 = 0
1:17/0 = 0
2:17/0 = 0
3:17/0 = 0
5:17/0 = 0
6:17/0 = 0
7:17/0 = 0
8:17/0 = 0
9:17/0 = 0
10:17/0 = 0
11:17/0 = 0
14:17/0 = 0
1:18/0 = 0
2:18/0 = 0
3:18/0 = 0
5:18/0 = 0
6:18/0 = 0
7:18/0 = 0
9:18/0 = 0
10:18/0 = 0
11:18/0 = 0
5:19/0 = 0
6:19/0 = 0
7:19/0 = 0
9:19/0 = 0
10:19/0 = 0
11:19/0 = 0
11:20/0 = 0

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_b8vfv"]
texture = ExtResource("1_0nmqi")
1:6/0 = 0
1:6/0/texture_origin = Vector2i(8, 8)
1:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-14.9902, -9.04622, -0.97093, -9.04622, -1.06565, -0.0473614, -14.8955, 0)
32:2/0 = 0
32:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -3.88372, -1.08934, -3.78899, -0.870518, 8, -8, 8)
32:3/0 = 0
32:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
32:4/0 = 0
32:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
31:4/0 = 0
31:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, -0.0693426, -0.832119, 0.20803, 8, -8, 8)
32:5/0 = 0
32:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
31:5/0 = 0
30:5/0 = 0
29:5/0 = 0
29:4/0 = 0
29:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
30:4/0 = 0
30:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
30:3/0 = 0
31:3/0 = 0
31:2/0 = 0
31:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, -0.0693426, -0.832119, 0.20803, 8, -8, 8)
33:2/0 = 0
33:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.994611, -3.93108, 8, -3.78899, 8, 8, 0.899885, 8)
34:2/0 = 0
34:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -3.88372, -1.08934, -3.78899, -0.870518, 8, -8, 8)
35:2/0 = 0
35:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.994611, -3.93108, 8, -3.78899, 8, 8, 0.899885, 8)
36:2/0 = 0
36:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -3.88372, -1.08934, -3.78899, -0.870518, 8, -8, 8)
36:3/0 = 0
36:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
36:4/0 = 0
36:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
35:4/0 = 0
35:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
34:5/0 = 0
34:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
33:5/0 = 0
33:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
33:4/0 = 0
33:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
34:3/0 = 0
34:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
35:3/0 = 0
35:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
34:4/0 = 0
34:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
33:3/0 = 0
33:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
34:6/0 = 0
34:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
35:6/0 = 0
35:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
36:6/0 = 0
36:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
37:6/0 = 0
37:6/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
37:5/0 = 0
37:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
36:5/0 = 0
36:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
35:5/0 = 0
35:5/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
37:4/0 = 0
37:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
37:3/0 = 0
37:3/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
37:2/0 = 0
37:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.994611, -3.93108, 8, -3.78899, 8, 8, 0.899885, 8)
37:1/0 = 0
37:0/0 = 0
36:1/0 = 0
35:1/0 = 0
34:1/0 = 0
33:1/0 = 0
32:1/0 = 0
32:0/0 = 0
33:0/0 = 0
34:0/0 = 0
35:0/0 = 0
36:0/0 = 0
28:4/0 = 0
28:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
27:4/0 = 0
27:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
26:4/0 = 0
26:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
25:4/0 = 0
25:4/0/physics_layer_0/polygon_0/points = PackedVector2Array(-0.138686, -0.693432, 8, -0.901463, 8, 8, -0.138686, 8)
27:5/0 = 0
26:5/0 = 0
25:2/0 = 0
25:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-0.138686, -0.693432, 8, -0.901463, 8, 8, -0.138686, 8)
26:2/0 = 0
26:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
27:2/0 = 0
27:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
28:2/0 = 0
28:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
29:2/0 = 0
29:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
30:2/0 = 0
30:2/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
26:3/0 = 0
27:3/0 = 0
28:3/0 = 0
29:3/0 = 0
28:5/0 = 0
26:1/0 = 0
27:1/0 = 0
28:1/0 = 0
29:1/0 = 0
30:1/0 = 0
26:0/0 = 0
26:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
27:0/0 = 0
27:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
28:0/0 = 0
28:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
29:0/0 = 0
29:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
30:0/0 = 0
30:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
31:0/0 = 0
31:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, -0.0693426, -0.832119, 0.20803, 8, -8, 8)
25:0/0 = 0
25:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-0.138686, -0.693432, 8, -0.901463, 8, 8, -0.138686, 8)
25:10/0 = 0
25:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-0.138686, -0.693432, 8, -0.901463, 8, 8, -0.138686, 8)
26:10/0 = 0
26:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
27:10/0 = 0
27:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
28:10/0 = 0
28:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
29:10/0 = 0
29:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
30:10/0 = 0
30:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
31:10/0 = 0
31:10/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, -0.0693426, -0.832119, 0.20803, 8, -8, 8)
32:10/0 = 0
33:10/0 = 0
34:10/0 = 0
35:10/0 = 0
36:10/0 = 0
37:10/0 = 0
37:11/0 = 0
36:11/0 = 0
35:11/0 = 0
34:11/0 = 0
34:12/0 = 0
34:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -3.88372, -1.08934, -3.78899, -0.870518, 8, -8, 8)
33:12/0 = 0
33:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.994611, -3.93108, 8, -3.78899, 8, 8, 0.899885, 8)
32:12/0 = 0
32:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -3.88372, -1.08934, -3.78899, -0.870518, 8, -8, 8)
32:11/0 = 0
33:11/0 = 0
31:11/0 = 0
30:11/0 = 0
29:11/0 = 0
28:11/0 = 0
27:11/0 = 0
26:11/0 = 0
25:13/0 = 0
25:12/0 = 0
25:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-0.138686, -0.693432, 8, -0.901463, 8, 8, -0.138686, 8)
26:12/0 = 0
26:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
27:12/0 = 0
27:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
28:12/0 = 0
28:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
29:12/0 = 0
29:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
30:12/0 = 0
30:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
31:12/0 = 0
31:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, -0.0693426, -0.832119, 0.20803, 8, -8, 8)
27:13/0 = 0
28:13/0 = 0
29:13/0 = 0
30:13/0 = 0
35:12/0 = 0
35:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.994611, -3.93108, 8, -3.78899, 8, 8, 0.899885, 8)
36:12/0 = 0
36:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -3.88372, -1.08934, -3.78899, -0.870518, 8, -8, 8)
36:13/0 = 0
36:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
37:13/0 = 0
37:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
37:14/0 = 0
37:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
37:15/0 = 0
37:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
37:16/0 = 0
37:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
37:12/0 = 0
37:12/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.994611, -3.93108, 8, -3.78899, 8, 8, 0.899885, 8)
35:13/0 = 0
35:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
35:14/0 = 0
35:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
35:15/0 = 0
35:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
36:15/0 = 0
36:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
36:14/0 = 0
36:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
36:16/0 = 0
36:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
35:16/0 = 0
35:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
34:13/0 = 0
34:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
33:13/0 = 0
33:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
33:14/0 = 0
33:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
34:14/0 = 0
34:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
34:15/0 = 0
34:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
34:16/0 = 0
34:16/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
33:15/0 = 0
33:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(0.899885, -8, 8, -8, 8, 8, 0.899885, 8)
32:15/0 = 0
32:15/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
32:14/0 = 0
32:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
32:13/0 = 0
32:13/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -8, -0.870518, -8, -0.870518, 8, -8, 8)
31:13/0 = 0
26:13/0 = 0
25:14/0 = 0
25:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-0.138686, -0.693432, 8, -0.901463, 8, 8, -0.138686, 8)
26:14/0 = 0
26:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
27:14/0 = 0
27:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
28:14/0 = 0
28:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
29:14/0 = 0
29:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
30:14/0 = 0
30:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, 8, -0.901463, 8, 8, -8, 8)
31:14/0 = 0
31:14/0/physics_layer_0/polygon_0/points = PackedVector2Array(-8, -0.901463, -0.0693426, -0.832119, 0.20803, 8, -8, 8)
31:15/0 = 0
30:15/0 = 0
29:15/0 = 0
28:15/0 = 0
27:15/0 = 0
26:15/0 = 0
25:15/0 = 0
25:11/0 = 0
25:5/0 = 0
25:1/0 = 0
25:3/0 = 0

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_mutkt"]
texture = ExtResource("5_o7aod")
0:0/animation_speed = 4.0
0:0/animation_frame_0/duration = 1.0
0:0/animation_frame_1/duration = 1.0
0:0/animation_frame_2/duration = 1.0
0:0/animation_frame_3/duration = 1.0
0:0/0 = 0

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_i7aag"]
texture = ExtResource("6_k8d7v")
0:0/animation_speed = 4.0
0:0/animation_frame_0/duration = 1.0
0:0/animation_frame_1/duration = 1.0
0:0/animation_frame_2/duration = 1.0
0:0/animation_frame_3/duration = 1.0
0:0/0 = 0

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_pldx1"]
texture = ExtResource("7_24gyc")
0:0/size_in_atlas = Vector2i(1, 2)
0:0/animation_speed = 4.0
0:0/animation_frame_0/duration = 1.0
0:0/animation_frame_1/duration = 1.0
0:0/animation_frame_2/duration = 1.0
0:0/animation_frame_3/duration = 1.0
0:0/0 = 0

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_eoofj"]
texture = ExtResource("8_y7yu3")
0:0/size_in_atlas = Vector2i(1, 2)
0:0/animation_speed = 4.0
0:0/animation_frame_0/duration = 1.0
0:0/animation_frame_1/duration = 1.0
0:0/animation_frame_2/duration = 1.0
0:0/animation_frame_3/duration = 1.0
0:0/0 = 0

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_5rjwk"]
texture = ExtResource("9_o3n45")
0:0/size_in_atlas = Vector2i(1, 2)
0:0/animation_speed = 4.0
0:0/animation_frame_0/duration = 1.0
0:0/animation_frame_1/duration = 1.0
0:0/animation_frame_2/duration = 1.0
0:0/animation_frame_3/duration = 1.0
0:0/0 = 0

[sub_resource type="TileSet" id="TileSet_a7m4y"]
physics_layer_0/collision_layer = 1
sources/1 = SubResource("TileSetAtlasSource_m55mf")
sources/2 = SubResource("TileSetAtlasSource_b8vfv")
sources/0 = SubResource("TileSetAtlasSource_24pqw")
sources/3 = SubResource("TileSetAtlasSource_mutkt")
sources/4 = SubResource("TileSetAtlasSource_i7aag")
sources/5 = SubResource("TileSetAtlasSource_pldx1")
sources/6 = SubResource("TileSetAtlasSource_eoofj")
sources/7 = SubResource("TileSetAtlasSource_5rjwk")

[sub_resource type="NavigationPolygon" id="NavigationPolygon_suyk4"]
vertices = PackedVector2Array(166, -182, 262, -182, 262, -154, 230, -154, 230, -102, 287, -102, 287, -86, 321, -86, 321, -102, 374, -102, 365.859, -98.4609, 598, -70, 598, -58, 566, -58, 465.617, -66.7734, 566, -26, 534, -26, 466.531, -38, 534, 6, 458, 6, 458, -38, 365.859, -38, 374, -38, 374, -26, 202, -26, 102, -102, 134, -102, 134, -26, 48.8438, -46.0938, 49.1328, -75.0469, 26, -26, 26, -46, 26, -75.0469, 26, -102, 58, -102, 102, -154, 58, -154, 166, -230, 26, -154, 26, -230, 202, -154, 365.859, -66.4609, 374, -98.6563, 365.859, -70, 383.883, -66.8984, 448.039, -66.8984, 614, -314, 554, -314, 554, -325.563, 560.383, -326, 614, -326, 294, -346, 266, -346, 266, -358, 294, -358, 714, -1158, 842, -1270, 1507.25, -1448.88, 714, -1050, 774, -1270, 714, -1210, 730, -1270, 774, -1306, 714, -982, 1552.72, 313.406, 842, -1386, -307.102, -1428.11, 806, -1386, 662, -1270, 678, -1210, 363.344, -134.461, 363.289, -133.961, 353.227, -131.359, 353.047, -135.867, 362, -390, 330, -134.758, 330, -390, 623.891, -346.859, 618, -346.781, 618, -346.992, 629, -358, 602, -346.781, 602, -346.992, 586, -346.781, 586, -346.992, 570, -346.781, 570, -347.047, 570, -420.969, 582, -421.023, 582, -420.961, 570, -420.898, 629, -390, 629.07, -390, 629, -374, 629.07, -374, 629.07, -358, 629.023, -346.898, 598, -421.023, 598, -420.961, 614, -421.023, 614, -420.938, 806, -1306, 730, -1306, 698, -1306, 698, -1386, 678, -1158, 678, -1050, 678, -1018, 666, -1018, 666, -1050, 538, -1158, 522, -1082, 522, -1094, 522, -1050, 394, -1094, 470, -1082, 470, -1050, 454, -1050, 394, -1062, 454, -1018, 406, -1018, 406, -986, 390, -986, 390, -954, 374, -954, 374, -822, 406, -822, 406, -794, 362, -806, 246, -794, 246, -730, 214, -730, 202, -774, 202, -806, 214, -678, 246, -678, 246, -602, 214, -602, 330, -582, 330, -614, 342, -614, 342, -538, 310, -538, 298, -582, 298, -486, 310, -486, 374, -422, 502, -422, 502, -410, 470, -410, 544.57, -346.93, 522, -358, 522, -390, 538, -390, 538, -406, 554.977, -406, 470, -314, 470, -282, 374, -282, 202, -602, 170, -602, 170, -678, 202, -678, 202, -730, 170, -730, 170, -774, 362, -922, 282, -922, 282, -954, 298, -998, 342, -1062, 266, -954, 266, -998, 298, -1094, 342, -1094, 522, -1158, 502, -1210, 502, -1162, -42, -1162, 666, 42, 666, -982, -323.898, 210.531, -42, 42, 555.047, -420.953, 629.023, -420.938, 629.07, -408.469, 374, -136.742, 365.367, -134.508, 470, -358, 533.969, -336.477, 526.211, -328.047, 525.273, -328.422, 662, -1386, 538, -1210, 512, -314, 534.367, -346.18, 362, -474, 374, -486, 202, -474, 214, -486)
polygons = Array[PackedInt32Array]([PackedInt32Array(0, 1, 2, 3), PackedInt32Array(4, 5, 6), PackedInt32Array(7, 8, 9, 10), PackedInt32Array(11, 12, 13, 14), PackedInt32Array(14, 13, 15, 16, 17), PackedInt32Array(16, 18, 19, 20, 17), PackedInt32Array(21, 22, 23, 24), PackedInt32Array(25, 26, 27, 28, 29), PackedInt32Array(27, 30, 31, 28), PackedInt32Array(29, 32, 33, 34), PackedInt32Array(25, 29, 34, 35), PackedInt32Array(35, 34, 36, 37), PackedInt32Array(36, 38, 39, 37), PackedInt32Array(35, 37, 0), PackedInt32Array(40, 35, 0), PackedInt32Array(40, 0, 3), PackedInt32Array(40, 3, 4, 24), PackedInt32Array(21, 24, 4, 6), PackedInt32Array(21, 6, 7, 41), PackedInt32Array(9, 42, 10), PackedInt32Array(7, 10, 43), PackedInt32Array(41, 7, 43), PackedInt32Array(44, 41, 43), PackedInt32Array(44, 43, 11, 45), PackedInt32Array(14, 45, 11), PackedInt32Array(46, 47, 48, 49, 50), PackedInt32Array(51, 52, 53, 54), PackedInt32Array(55, 56, 57, 58), PackedInt32Array(59, 56, 55, 60), PackedInt32Array(59, 60, 61, 62), PackedInt32Array(63, 58, 57, 64), PackedInt32Array(57, 56, 65), PackedInt32Array(66, 57, 65, 67), PackedInt32Array(68, 61, 60, 69), PackedInt32Array(70, 71, 72, 73, 74), PackedInt32Array(73, 75, 76, 74), PackedInt32Array(77, 78, 79, 80), PackedInt32Array(79, 81, 82), PackedInt32Array(82, 83, 84), PackedInt32Array(84, 85, 86), PackedInt32Array(87, 88, 89, 90), PackedInt32Array(91, 92, 93), PackedInt32Array(93, 94, 80), PackedInt32Array(80, 95, 96, 77), PackedInt32Array(89, 97, 98), PackedInt32Array(98, 99, 100), PackedInt32Array(67, 101, 62), PackedInt32Array(62, 61, 102, 67), PackedInt32Array(102, 103, 104, 67), PackedInt32Array(105, 55, 58, 106), PackedInt32Array(106, 107, 108, 109), PackedInt32Array(110, 105, 106, 109, 111, 112), PackedInt32Array(109, 113, 111), PackedInt32Array(114, 115, 116, 117, 118), PackedInt32Array(117, 119, 120, 118), PackedInt32Array(120, 121, 122, 118), PackedInt32Array(122, 123, 124), PackedInt32Array(125, 126, 127, 128), PackedInt32Array(129, 130, 131, 132, 133), PackedInt32Array(134, 135, 136, 137), PackedInt32Array(138, 139, 140, 141), PackedInt32Array(138, 141, 142, 143), PackedInt32Array(144, 143, 142, 145), PackedInt32Array(146, 147, 148, 149), PackedInt32Array(150, 151, 152, 153, 86), PackedInt32Array(153, 154, 155, 93, 80, 79, 82), PackedInt32Array(156, 157, 158, 74), PackedInt32Array(159, 160, 161, 162), PackedInt32Array(163, 164, 165, 132), PackedInt32Array(166, 167, 168, 169, 170, 118, 124), PackedInt32Array(168, 171, 172, 169), PackedInt32Array(169, 173, 174, 170), PackedInt32Array(112, 175, 110), PackedInt32Array(176, 177, 178), PackedInt32Array(179, 180, 63, 64), PackedInt32Array(179, 64, 181, 182), PackedInt32Array(182, 181, 66, 178), PackedInt32Array(155, 183, 90, 93), PackedInt32Array(100, 184, 185, 91), PackedInt32Array(186, 187, 70, 74, 158), PackedInt32Array(146, 149, 188, 74), PackedInt32Array(189, 190, 191), PackedInt32Array(66, 67, 104), PackedInt32Array(66, 104, 192), PackedInt32Array(176, 178, 66, 192, 68), PackedInt32Array(193, 176, 68), PackedInt32Array(193, 68, 69, 105, 110), PackedInt32Array(112, 111, 115, 114), PackedInt32Array(89, 98, 100, 91, 93, 90), PackedInt32Array(191, 194, 156, 74), PackedInt32Array(189, 191, 74, 188), PackedInt32Array(153, 82, 84, 86), PackedInt32Array(151, 150, 195), PackedInt32Array(151, 195, 189, 188), PackedInt32Array(146, 74, 196, 197), PackedInt32Array(145, 197, 196, 198), PackedInt32Array(144, 145, 198), PackedInt32Array(199, 144, 198), PackedInt32Array(199, 198, 159, 137), PackedInt32Array(134, 137, 159, 162), PackedInt32Array(131, 134, 162, 163), PackedInt32Array(131, 163, 132), PackedInt32Array(129, 133, 128, 127), PackedInt32Array(125, 128, 166, 124), PackedInt32Array(122, 124, 118)])
outlines = Array[PackedVector2Array]([PackedVector2Array(-317, -1438, 1517, -1459, 1563, 324, -334, 220)])
source_geometry_mode = 1

[node name="Dungeon" type="Node2D" groups=["navigation_polygon_source_geometry_group"]]
y_sort_enabled = true

[node name="Floor1" type="TileMapLayer" parent="."]
z_index = -1
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_e2y05")

[node name="Floor2" type="TileMapLayer" parent="."]
z_index = -1
tile_map_data = PackedByteArray("AAAgAOr/AAATAAcAAAAhAOn/AAATAAcAAAAkAOf/AAAEAAsAAAAlAOf/AAAFAAsAAAAhAOj/AAATAAcAAAAgAOn/AAATAAoAAAAgAOj/AAATAAcAAAAhAOf/AAATAAoAAAAhAOb/AAATAAoAAAAgAOf/AAATAAcAAAAgAOb/AAATAAkAAAAhAOX/AAATAAYAAAAZAMX/AQACAAUAAAAYAMX/AQABAAUAAAA=")
tile_set = SubResource("TileSet_e2y05")

[node name="YSortWalls" type="TileMapLayer" parent="."]
y_sort_enabled = true
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_a7m4y")

[node name="YSortWalls2" type="TileMapLayer" parent="."]
y_sort_enabled = true
tile_map_data = PackedByteArray("AAAMAPL/AAAAAAAAAAAPAPL/AAAAAAAAAAABAO//AAAAAAAAAAAHAO//AAAAAAAAAAAdAPn/AAAAAAAAAAAgAPn/AAAAAAAAAAAjAPn/AAAAAAAAAAAQAOv/AAAAAAAAAAASAOv/AAAAAAAAAAAnAOz/AAAAAAAAAAAlAOz/AAAAAAAAAAAjAOz/AAAAAAAAAAACAPz/AgABAAYAAAAUAPj/BwAAAAAAAAAXAPj/BwAAAAAAAAAlALb/BwAAAAAAAAAoALb/BwAAAAAAAAA=")
tile_set = SubResource("TileSet_a7m4y")

[node name="Player" parent="." instance=ExtResource("4_hpnkm")]
position = Vector2(54, -64)

[node name="SelectedToolPanel" parent="." instance=ExtResource("5_a7m4y")]

[node name="InventoryMenu" parent="." instance=ExtResource("6_prosc")]

[node name="PlayerStatusPanel" parent="." instance=ExtResource("7_rjm1x")]

[node name="MapPanel" parent="." instance=ExtResource("8_prosc")]

[node name="DungeonPortal" parent="." instance=ExtResource("9_rjm1x")]
position = Vector2(32, -64)
scale = Vector2(0.865, 2.215)
SceneFrom = 2
SceneTo = 0

[node name="DungeonManager" parent="." instance=ExtResource("10_dungeonmanager")]

[node name="DungeonDroppedResourceManager" parent="." instance=ExtResource("12_droppedresourcemanager")]

[node name="MenuManager" parent="." instance=ExtResource("11_ngpfh")]

[node name="Objects" type="Node2D" parent="."]
y_sort_enabled = true

[node name="Door1" type="Node2D" parent="Objects"]
y_sort_enabled = true

[node name="DoorForSwitch" parent="Objects/Door1" instance=ExtResource("13_ilsmu")]
y_sort_enabled = true
position = Vector2(352, -120)
SwitchName = "Dungeon1Switch"

[node name="Switch" parent="Objects/Door1" instance=ExtResource("14_lj2h2")]
y_sort_enabled = true
position = Vector2(304, -109)
SwitchName = "Dungeon1Switch"

[node name="Door2" type="Node2D" parent="Objects"]
y_sort_enabled = true

[node name="DoorForSwitch" parent="Objects/Door2" instance=ExtResource("13_ilsmu")]
y_sort_enabled = true
position = Vector2(216, -568)
SwitchName = "Dungeon2Switch"

[node name="Switch" parent="Objects/Door2" instance=ExtResource("14_lj2h2")]
y_sort_enabled = true
position = Vector2(336, -615)
SwitchName = "Dungeon2Switch"

[node name="Traps" type="Node2D" parent="Objects"]
z_index = -1

[node name="DungeonGroundTrap" parent="Objects/Traps" instance=ExtResource("15_lj2h2")]
position = Vector2(208, -136)

[node name="DungeonGroundTrap2" parent="Objects/Traps" instance=ExtResource("15_lj2h2")]
position = Vector2(224, -136)

[node name="MovingDungeonTrap" parent="Objects/Traps" instance=ExtResource("16_jogau")]
position = Vector2(327, -165)
StartFromRight = false

[node name="Left" type="Node2D" parent="Objects/Traps/MovingDungeonTrap"]

[node name="Right" type="Node2D" parent="Objects/Traps/MovingDungeonTrap"]
position = Vector2(48, 0)

[node name="MovingDungeonTrap2" parent="Objects/Traps" instance=ExtResource("16_jogau")]
position = Vector2(327, -215)
StartFromRight = false

[node name="Left" type="Node2D" parent="Objects/Traps/MovingDungeonTrap2"]

[node name="Right" type="Node2D" parent="Objects/Traps/MovingDungeonTrap2"]
position = Vector2(48, 0)

[node name="MovingDungeonTrap3" parent="Objects/Traps" instance=ExtResource("16_jogau")]
position = Vector2(327, -190)

[node name="Left" type="Node2D" parent="Objects/Traps/MovingDungeonTrap3"]

[node name="Right" type="Node2D" parent="Objects/Traps/MovingDungeonTrap3"]
position = Vector2(48, 0)

[node name="NavigationRegion2D" type="NavigationRegion2D" parent="."]
navigation_polygon = SubResource("NavigationPolygon_suyk4")

[gd_scene load_steps=16 format=3 uid="uid://dbr5otyrsi4yu"]

[ext_resource type="Script" uid="uid://bw54b5f48bl4r" path="res://scenes/regions/region7/Dungeon/DungeonBat.cs" id="1_dungeonbat_script"]
[ext_resource type="Texture2D" uid="uid://sxug5i6v67ow" path="res://resources/solaria/enemies/Bat 01.png" id="2_2gyhg"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="2_progressbar"]

[sub_resource type="Animation" id="Animation_vid1h"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="Animation" id="Animation_yxir5"]
resource_name = "Left"
length = 0.4
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [12, 13, 14, 15]
}

[sub_resource type="Animation" id="Animation_2gyhg"]
resource_name = "Right"
length = 0.4
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [21, 22, 23, 24]
}

[sub_resource type="Animation" id="Animation_nnjdi"]
resource_name = "Down"
length = 0.7
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3, 4, 5, 6]
}

[sub_resource type="Animation" id="Animation_l4pup"]
resource_name = "Up"
length = 0.4
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [30, 31, 32, 33]
}

[sub_resource type="Animation" id="Animation_3oucy"]
resource_name = "GotHitDown"
length = 0.2
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [7, 8]
}

[sub_resource type="Animation" id="Animation_da5id"]
resource_name = "GotHitLeft"
length = 0.2
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [16, 17]
}

[sub_resource type="Animation" id="Animation_8cmq3"]
resource_name = "GotHitRight"
length = 0.2
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [25, 26]
}

[sub_resource type="Animation" id="Animation_5p0ha"]
resource_name = "GotHitUp"
length = 0.2
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [34, 35]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_bat"]
_data = {
&"Down": SubResource("Animation_nnjdi"),
&"GotHitDown": SubResource("Animation_3oucy"),
&"GotHitLeft": SubResource("Animation_da5id"),
&"GotHitRight": SubResource("Animation_8cmq3"),
&"GotHitUp": SubResource("Animation_5p0ha"),
&"Left": SubResource("Animation_yxir5"),
&"RESET": SubResource("Animation_vid1h"),
&"Right": SubResource("Animation_2gyhg"),
&"Up": SubResource("Animation_l4pup")
}

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_collision"]
radius = 4.0
height = 8.0

[sub_resource type="CircleShape2D" id="CircleShape2D_detection"]
radius = 80.0

[node name="DungeonBat" type="CharacterBody2D" groups=["dungeon_enemies"]]
y_sort_enabled = true
script = ExtResource("1_dungeonbat_script")

[node name="Sprite2D" type="Sprite2D" parent="."]
y_sort_enabled = true
texture = ExtResource("2_2gyhg")
hframes = 9
vframes = 4

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_bat")
}

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CapsuleShape2D_collision")

[node name="DetectionArea" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 4

[node name="CollisionShape2D" type="CollisionShape2D" parent="DetectionArea"]
shape = SubResource("CircleShape2D_detection")

[node name="ProgressBar" parent="." instance=ExtResource("2_progressbar")]
position = Vector2(0, -12)
scale = Vector2(1, 0.4375)

[node name="WanderTimer" type="Timer" parent="."]
wait_time = 3.0
one_shot = true

[node name="AttackTimer" type="Timer" parent="."]
one_shot = true

[node name="AttackWindupTimer" type="Timer" parent="."]
wait_time = 0.5
one_shot = true

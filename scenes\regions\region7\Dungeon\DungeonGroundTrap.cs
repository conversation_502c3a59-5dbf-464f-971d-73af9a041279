using Godot;
using System;

public partial class DungeonGroundTrap : Node2D
{
	[Export] public int InitialDelayMs { get; set; } = 0;
	[Export] public float StateSwitchDelay { get; set; } = 2.0f;
	[Export] public int Damage { get; set; } = 1;
	[Export] public float KnockbackDistance { get; set; } = 8.0f;

	private AnimationPlayer _animationPlayer;
	private Area2D _area2D;
	private Timer _stateTimer;
	private Timer _initialDelayTimer;
	private bool _isActive = false;
	private bool _isShowing = false;
	private bool _hasStarted = false;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_area2D = GetNode<Area2D>("Area2D");

		if (_animationPlayer == null)
		{
			GD.PrintErr("DungeonGroundTrap: AnimationPlayer not found!");
			return;
		}

		if (_area2D == null)
		{
			GD.PrintErr("DungeonGroundTrap: Area2D not found!");
			return;
		}

		_area2D.AreaEntered += OnAreaEntered;
		_animationPlayer.AnimationFinished += OnAnimationFinished;

		SetupTimers();
		
		_animationPlayer.Play("RESET");
		
		if (InitialDelayMs > 0)
		{
			_initialDelayTimer.WaitTime = InitialDelayMs / 1000.0f;
			_initialDelayTimer.Start();
		}
		else
		{
			StartTrapCycle();
		}
	}

	private void SetupTimers()
	{
		_stateTimer = new Timer();
		_stateTimer.OneShot = true;
		_stateTimer.Timeout += OnStateTimerTimeout;
		AddChild(_stateTimer);

		_initialDelayTimer = new Timer();
		_initialDelayTimer.OneShot = true;
		_initialDelayTimer.Timeout += OnInitialDelayTimeout;
		AddChild(_initialDelayTimer);
	}

	private void OnInitialDelayTimeout()
	{
		StartTrapCycle();
	}

	private void StartTrapCycle()
	{
		if (_hasStarted) return;
		_hasStarted = true;
		
		ShowTrap();
	}

	private void ShowTrap()
	{
		_isShowing = true;
		_isActive = false;
		_animationPlayer.Play("Show");
	}

	private void HideTrap()
	{
		_isShowing = false;
		_isActive = false;
		_animationPlayer.Play("Hide");
	}

	private void OnAnimationFinished(StringName animationName)
	{
		if (animationName == "Show")
		{
			_isActive = true;
			_stateTimer.WaitTime = StateSwitchDelay;
			_stateTimer.Start();
		}
		else if (animationName == "Hide")
		{
			_stateTimer.WaitTime = StateSwitchDelay;
			_stateTimer.Start();
		}
	}

	private void OnStateTimerTimeout()
	{
		if (_isShowing && _isActive)
		{
			HideTrap();
		}
		else if (!_isShowing)
		{
			ShowTrap();
		}
	}

	private void OnAreaEntered(Area2D area)
	{
		if (!_isActive) return;

		if (area.Name == "PlayerDetector")
		{
			var parent = area.GetParent();
			if (parent is DungeonPlayerController dungeonPlayer)
			{
				dungeonPlayer.TakeDamage(Damage);
				ApplyKnockbackToPlayer(dungeonPlayer);
				ApplyDamageModulation(dungeonPlayer);
				GD.Print($"DungeonGroundTrap: Player hit for {Damage} damage!");
			}
		}
	}

	private void ApplyKnockbackToPlayer(DungeonPlayerController player)
	{
		Vector2 knockbackDirection;

		if (player.Velocity.Length() > 0)
		{
			knockbackDirection = -player.Velocity.Normalized();
		}
		else
		{
			knockbackDirection = (player.GlobalPosition - GlobalPosition).Normalized();
		}

		Vector2 targetPosition = player.GlobalPosition + knockbackDirection * KnockbackDistance;

		var tween = CreateTween();
		tween.TweenProperty(player, "global_position", targetPosition, 0.2f);
		tween.SetEase(Tween.EaseType.Out);
		tween.SetTrans(Tween.TransitionType.Quart);
	}

	private void ApplyDamageModulation(DungeonPlayerController player)
	{
		var sprite = player.GetNode<Sprite2D>("PlayerSprite");
		if (sprite == null) return;

		var originalColor = sprite.Modulate;
		sprite.Modulate = new Color(1.5f, 0.6f, 0.6f, 1.0f);

		GetTree().CreateTimer(0.7f).Timeout += () => {
			if (IsInstanceValid(sprite))
			{
				sprite.Modulate = originalColor;
			}
		};
	}

	public override void _ExitTree()
	{
		if (_area2D != null)
		{
			_area2D.AreaEntered -= OnAreaEntered;
		}

		if (_animationPlayer != null)
		{
			_animationPlayer.AnimationFinished -= OnAnimationFinished;
		}

		_stateTimer?.QueueFree();
		_initialDelayTimer?.QueueFree();

		base._ExitTree();
	}
}

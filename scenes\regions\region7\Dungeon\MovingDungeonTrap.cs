using Godot;
using System;

public partial class MovingDungeonTrap : Node2D
{
	[Export] public int Damage { get; set; } = 1;
	[Export] public float Speed { get; set; } = 50.0f;
	[Export] public bool StartFromRight { get; set; } = true;

	private AnimationPlayer _animationPlayer;
	private Area2D _area2D;
	private Sprite2D _sprite2D;
	private Node2D _leftPoint;
	private Node2D _rightPoint;
	private Timer _waitTimer;

	private bool _movingRight;
	private bool _isWaiting = false;
	private Vector2 _targetPosition;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_sprite2D = GetNode<Sprite2D>("Sprite2D");
		_area2D = GetNode<Area2D>("Sprite2D/Area2D");
		_leftPoint = GetNode<Node2D>("Left");
		_rightPoint = GetNode<Node2D>("Right");

		if (_animationPlayer == null)
		{
			GD.PrintErr("MovingDungeonTrap: AnimationPlayer not found!");
			return;
		}

		if (_sprite2D == null)
		{
			GD.PrintErr("MovingDungeonTrap: Sprite2D not found!");
			return;
		}

		if (_area2D == null)
		{
			GD.PrintErr("MovingDungeonTrap: Area2D not found!");
			return;
		}

		if (_leftPoint == null)
		{
			GD.PrintErr("MovingDungeonTrap: Left Node2D not found!");
			return;
		}

		if (_rightPoint == null)
		{
			GD.PrintErr("MovingDungeonTrap: Right Node2D not found!");
			return;
		}

		_area2D.AreaEntered += OnAreaEntered;
		
		SetupWaitTimer();
		
		_animationPlayer.Play("Animate");

		_movingRight = StartFromRight;
		if (StartFromRight)
		{
			_sprite2D.GlobalPosition = _rightPoint.GlobalPosition;
			_targetPosition = _rightPoint.GlobalPosition;
		}
		else
		{
			_sprite2D.GlobalPosition = _leftPoint.GlobalPosition;
			_targetPosition = _leftPoint.GlobalPosition;
		}
	}

	private void SetupWaitTimer()
	{
		_waitTimer = new Timer();
		_waitTimer.OneShot = true;
		_waitTimer.WaitTime = 0.5f;
		_waitTimer.Timeout += OnWaitTimerTimeout;
		AddChild(_waitTimer);
	}

	public override void _Process(double delta)
	{
		if (_isWaiting) return;

		Vector2 direction = (_targetPosition - _sprite2D.GlobalPosition).Normalized();
		float distanceToTarget = _sprite2D.GlobalPosition.DistanceTo(_targetPosition);

		if (distanceToTarget <= 2.0f)
		{
			_sprite2D.GlobalPosition = _targetPosition;
			StartWaiting();
		}
		else
		{
			_sprite2D.GlobalPosition += direction * Speed * (float)delta;
		}
	}

	private void StartWaiting()
	{
		_isWaiting = true;
		_waitTimer.Start();
	}

	private void OnWaitTimerTimeout()
	{
		_isWaiting = false;
		
		if (_movingRight)
		{
			_targetPosition = _leftPoint.GlobalPosition;
			_movingRight = false;
		}
		else
		{
			_targetPosition = _rightPoint.GlobalPosition;
			_movingRight = true;
		}
	}

	private void OnAreaEntered(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			var parent = area.GetParent();
			if (parent is DungeonPlayerController dungeonPlayer)
			{
				dungeonPlayer.TakeDamage(Damage);
				ApplyKnockbackToPlayer(dungeonPlayer);
				ApplyDamageModulation(dungeonPlayer);
				GD.Print($"MovingDungeonTrap: Player hit for {Damage} damage!");
			}
		}
	}

	private void ApplyKnockbackToPlayer(DungeonPlayerController player)
	{
		Vector2 knockbackDirection;
		
		if (player.Velocity.Length() > 0)
		{
			knockbackDirection = -player.Velocity.Normalized();
		}
		else
		{
			knockbackDirection = (player.GlobalPosition - GlobalPosition).Normalized();
		}
		
		Vector2 targetPosition = player.GlobalPosition + knockbackDirection * 8.0f;

		var tween = CreateTween();
		tween.TweenProperty(player, "global_position", targetPosition, 0.2f);
		tween.SetEase(Tween.EaseType.Out);
		tween.SetTrans(Tween.TransitionType.Quart);
	}

	private void ApplyDamageModulation(DungeonPlayerController player)
	{
		var sprite = player.GetNode<Sprite2D>("PlayerSprite");
		if (sprite == null) return;

		var originalColor = sprite.Modulate;
		sprite.Modulate = new Color(1.5f, 0.6f, 0.6f, 1.0f);

		GetTree().CreateTimer(0.7f).Timeout += () => {
			if (IsInstanceValid(sprite))
			{
				sprite.Modulate = originalColor;
			}
		};
	}

	public override void _ExitTree()
	{
		if (_area2D != null)
		{
			_area2D.AreaEntered -= OnAreaEntered;
		}

		_waitTimer?.QueueFree();

		base._ExitTree();
	}
}

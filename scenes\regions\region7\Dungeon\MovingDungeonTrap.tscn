[gd_scene load_steps=7 format=3 uid="uid://cf1vpvs2pqts5"]

[ext_resource type="Script" path="res://scenes/regions/region7/Dungeon/MovingDungeonTrap.cs" id="1_movingdungeontrap_script"]
[ext_resource type="Texture2D" uid="uid://cwfcasyedqbtb" path="res://resources/solaria/crypt/trap_2.png" id="1_70g2g"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_70g2g"]
size = Vector2(12, 5)

[sub_resource type="Animation" id="Animation_70g2g"]
resource_name = "Animate"
length = 0.4
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3]
}

[sub_resource type="Animation" id="Animation_s8s16"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_kwb03"]
_data = {
&"Animate": SubResource("Animation_70g2g"),
&"RESET": SubResource("Animation_s8s16")
}

[node name="MovingDungeonTrap" type="Node2D"]
script = ExtResource("1_movingdungeontrap_script")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("1_70g2g")
hframes = 4

[node name="Area2D" type="Area2D" parent="Sprite2D"]
collision_layer = 0
collision_mask = 4

[node name="CollisionShape2D" type="CollisionShape2D" parent="Sprite2D/Area2D"]
position = Vector2(0, -2.5)
shape = SubResource("RectangleShape2D_70g2g")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_kwb03")
}

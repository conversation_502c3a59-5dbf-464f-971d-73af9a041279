using Godot;

public partial class Switch : Node2D
{
	[Export] public string SwitchName { get; set; } = "DefaultSwitch";

	private AnimationPlayer _animationPlayer;
	private Area2D _area2D;
	private bool _isPlayerInArea = false;
	private bool _isOpen = false;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_area2D = GetNode<Area2D>("Area2D");

		if (_area2D != null)
		{
			_area2D.CollisionMask = 4;
			_area2D.AreaEntered += OnAreaEntered;
			_area2D.AreaExited += OnAreaExited;
		}

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.EKeyPressed += OnEKeyPressed;
		}

		GD.Print($"Switch '{SwitchName}' initialized");
	}

	private void OnAreaEntered(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			var parent = area.GetParent();
			if (parent is DungeonPlayerController)
			{
				_isPlayerInArea = true;
				CommonSignals.Instance?.EmitShowKeyEPrompt();
				GD.Print($"Switch '{SwitchName}': Player entered area, showing KeyE prompt");
			}
		}
	}

	private void OnAreaExited(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			var parent = area.GetParent();
			if (parent is DungeonPlayerController)
			{
				_isPlayerInArea = false;
				CommonSignals.Instance?.EmitHideKeyEPrompt();
				GD.Print($"Switch '{SwitchName}': Player left area, hiding KeyE prompt");
			}
		}
	}

	private void OnEKeyPressed()
	{
		if (!_isPlayerInArea) return;

		if (!_isOpen)
		{
			OpenSwitch();
		}
		else
		{
			CloseSwitch();
		}
	}

	private void OpenSwitch()
	{
		_isOpen = true;
		
		if (_animationPlayer != null && _animationPlayer.HasAnimation("Open"))
		{
			_animationPlayer.Play("Open");
			GD.Print($"Switch '{SwitchName}': Playing Open animation");
		}

		CommonSignals.Instance?.EmitSwitchPressed(SwitchName);
		GD.Print($"Switch '{SwitchName}': Emitted SwitchPressed signal for opening");
	}

	private void CloseSwitch()
	{
		_isOpen = false;
		
		if (_animationPlayer != null && _animationPlayer.HasAnimation("Close"))
		{
			_animationPlayer.Play("Close");
			GD.Print($"Switch '{SwitchName}': Playing Close animation");
		}

		CommonSignals.Instance?.EmitSwitchPressed(SwitchName);
		GD.Print($"Switch '{SwitchName}': Emitted SwitchPressed signal for closing");
	}

	public bool IsOpen()
	{
		return _isOpen;
	}

	public override void _ExitTree()
	{
		if (_area2D != null)
		{
			_area2D.AreaEntered -= OnAreaEntered;
			_area2D.AreaExited -= OnAreaExited;
		}

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.EKeyPressed -= OnEKeyPressed;
		}

		base._ExitTree();
	}
}

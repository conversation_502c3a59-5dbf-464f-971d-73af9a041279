Task-1:
I added a trap (DungeonGroundTrap.tscn). It is like spikes appearing and hiding from the ground. So this trap will be active instantly when game starts. Flow be like this: 
1) initially trap is hidden
2) it should have delay that i can add in inspector in milliseconds to indicate initial delay when the trap starts working
3) i need to be able to set state switch delay in inspector. this means: when trap opens - then wait this delay before hiding it - then wait this delay before opening it again (default 2s, but adjustable i).
4) trap has area2d - when player is in area2d of trap then it should take damage to player. Amount of damage should also be set in inspector (default 1). Also when player is hit, he should be knocked back and player should be modulated to slightly red color (to show that he was damaged). Knocked back to opposite direction that player was moving/facing to. Basically it has to be around 8pixels knockback -> best if it can be also adjustable in inspector.
5) how to handle appearing and hiding spikes - DungeonGroundTrap has node called AnimationPlayer that has animations: Hide and Show. It handles showing/hiding spikes. Trap is active (can attack player) when Show animation finishes - when it finishes start timer to hide it. When Hide animation finishes - start timer to show it again.
6) make sure Area2D is listening on same layer as dungeon player - adjust it in trap if needed.

7) make sure the project builds.
8) read again all required files to make sure you didn't miss anything
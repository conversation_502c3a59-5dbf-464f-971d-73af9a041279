TASK-1:
look what we implemented in task59.md - DungeonGroundTrap (cs, tscn). Now, we will be implementing a moving trap. It will work as follow:
1) we have MovingDungeonTrap.tscn. It has animation player with animation Animate. This animation is looped. Play it at _ready. Trap will have in sprite2d->Area2D. Handle this area like in DungeonGroundTrap and as i described in task59.md - proper player detection, layer etc. This trap is active all the time, not like task59.md where it was hidden at start.
2) i will add this trap in other scenes and as child i will add 2 Node2D: Left and Right. That you need to get poins from them that informs about max left and max right position of the trap. Trap will move from Left to Right and back. When it reaches Left or Right then it should change direction. Trap will move at constant speed. When it reaches Left or Right then it should change direction. Trap will move from Left to Right and back. When it reaches Left or Right then it should change direction. Trap will move at constant speed.